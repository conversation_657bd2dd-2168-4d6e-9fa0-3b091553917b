from fastapi import FastAP<PERSON>, HTTPException, Request
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
import uuid
from datetime import datetime
from fastapi.responses import JSONResponse, StreamingResponse
from pydantic import BaseModel, Field
import requests
from dotenv import load_dotenv
from pymongo import MongoClient
from langgraph.graph import StateGraph
from bson.objectid import ObjectId
import os
import json
import sys
import re
import logging
import redis
import ssl
import asyncio
from app.utils import handle_greeting
from app.guardrail import run_guardrails, output_guardrails, clean_llm_output
from enum import Enum
# from langchain_mcp_adapters.client import MultiServerMCPClient  # Commented out due to compatibility issues
from server.qdrant_mcp_wrapper import qdrant_mcp_client, test_qdrant_mcp_connection, get_qdrant_tool
from server.dynamic_mcp_factory import (
    dynamic_mcp_factory,
    create_dynamic_mcp_server,
    start_dynamic_mcp_server,
    delete_dynamic_mcp_server,
    get_dynamic_mcp_server_info
)
from server.dynamic_agent_manager import (
    create_dynamic_agent,
    query_user_dynamic_agent,
    get_all_dynamic_agents,
    delete_dynamic_agent
)
from aggregator_agent import get_aggregator_agent
from memory_manager import get_memory_manager
from planning_manager import get_planning_manager
from generative_model import get_generative_model
from typing import TypedDict

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
# Load environment variables
load_dotenv()

# Configuration
MONGO_URL = os.getenv("MONGO_URL")
MONGO_DB = os.getenv("MONGO_DB", "spaarxsense")
MONGO_COLLECTION = os.getenv("MONGO_COLLECTION", "userconversations")
QDRANT_DEFAULT_COLLECTION = os.getenv("COLLECTION_NAME", "Gen AI")
MAX_TOKENS = 8192

# AWS Redis Configuration
REDIS_HOST = os.getenv("REDIS_HOST")
REDIS_PORT = int(os.getenv("REDIS_PORT", 6379))
REDIS_DB = int(os.getenv("REDIS_DB", 0))
REDIS_PASSWORD = os.getenv("REDIS_PASSWORD", "SpaarxsenseAgenticRetrieval")
SESSION_TTL = int(os.getenv("SESSION_TTL", 900))
REDIS_SSL = os.getenv("REDIS_SSL", "true").lower() == "true"

# Supported hosts
SUPPORTED_HOSTS = ["spaarxsenseaifabric", "groq", "google", "meta", "microsoft", "anthropic", "openai"]
SPAARXSENSE_AI_FABRIC_MODELS = ["deepseek-r1:1.5b"]

# Endpoints
SPAARXSENSE_AI_FABRIC_ENDPOINT = os.getenv("SPAARXSENSE_AI_FABRIC_ENDPOINT")
GROQ_ENDPOINT = os.getenv("GROQ_ENDPOINT", "https://api.groq.com/openai/v1/chat/completions")
GOOGLE_ENDPOINT = os.getenv("GOOGLE_ENDPOINT", "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent")
OPENAI_ENDPOINT = os.getenv("OPENAI_ENDPOINT", "https://api.openai.com/v1/chat/completions")
HUGGINGFACE_ENDPOINT = os.getenv("HUGGINGFACE_ENDPOINT", "https://api-inference.huggingface.co/models")
ANTHROPIC_ENDPOINT = os.getenv("ANTHROPIC_ENDPOINT", "https://api.anthropic.com/v1/messages")
QDRANT_URL = os.getenv("QDRANT_URL")
SERPER_MCP_URL = "https://google.serper.dev/search"

# Initialize FastAPI app
app = FastAPI(title="MCP Retrieval-SpaarxSense")

# Initialize Redis client
print("\n" + "="*60)
print("🔄 REDIS CONNECTION STATUS")
print("="*60)
logger.info(f"Redis Host: {REDIS_HOST}")
logger.info(f"Redis Port: {REDIS_PORT}")
logger.info(f"Redis SSL: {REDIS_SSL}")

redis_client = None
try:
    logger.info("🔗 Attempting Redis connection...")
    redis_client = redis.Redis(
        host=REDIS_HOST,
        port=REDIS_PORT,
        password=REDIS_PASSWORD,
        db=REDIS_DB,
        ssl=REDIS_SSL,
        ssl_cert_reqs=None,
        ssl_check_hostname=False,
        decode_responses=True,
        socket_connect_timeout=10,
        socket_timeout=10,
        retry_on_timeout=True,
        ssl_min_version=ssl.TLSVersion.TLSv1_2
    )
    response = redis_client.ping()
    logger.info(f"✅ Redis connection successful: PING = {response}")
    print("✅ Redis Status: CONNECTED")
except redis.AuthenticationError as e:
    logger.error(f"❌ Redis authentication failed: {str(e)}")
    print("❌ Redis Status: AUTHENTICATION FAILED")
    redis_client = None
except redis.ConnectionError as e:
    logger.error(f"❌ Redis connection failed: {str(e)}")
    print("❌ Redis Status: CONNECTION FAILED")
    redis_client = None
except Exception as e:
    logger.error(f"❌ Redis error: {str(e)}")
    print("❌ Redis Status: ERROR")
    redis_client = None

if redis_client is None:
    print("⚠️ Redis Status: RUNNING IN MONGODB-ONLY MODE")
print("="*60)

# Test Redis connection
def test_redis_connection():
    logger.info("🔄 Testing Redis connection...")
    if redis_client is None:
        logger.warning("⚠️ Redis client not initialized - running in MongoDB-only mode")
        return False
    try:
        response = redis_client.ping()
        logger.info(f"✅ Connected to AWS Redis successfully: PING response = {response}")
        test_key = "health_check"
        redis_client.set(test_key, "ok", ex=10)
        test_value = redis_client.get(test_key)
        logger.info(f"✅ AWS Redis health check: set/get test successful - {test_value}")
        redis_client.delete(test_key)
        logger.info("🎉 Redis connection test completed successfully!")
        return True
    except redis.AuthenticationError as e:
        logger.error(f"❌ AWS Redis authentication failed: {str(e)}")
        logger.warning("⚠️ Continuing in MongoDB-only mode")
        return False
    except redis.ConnectionError as e:
        logger.error(f"❌ Failed to connect to AWS Redis: {str(e)}")
        logger.warning("⚠️ Continuing in MongoDB-only mode")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error connecting to AWS Redis: {str(e)}")
        logger.warning("⚠️ Continuing in MongoDB-only mode")
        return False

# MCP client will be initialized during app startup
mcp_client = None

def initialize_mcp_client():
    """Initialize MCP client during app startup"""
    global mcp_client
    print("\n" + "="*60)
    print("🔄 QDRANT MCP CONNECTION STATUS")
    print("="*60)
    logger.info(f"Qdrant URL: {QDRANT_URL}")
    logger.info(f"Default Collection: {QDRANT_DEFAULT_COLLECTION}")

    try:
        logger.info("🔗 Initializing Qdrant MCP client...")
        # Using custom Qdrant MCP wrapper for better compatibility
        mcp_client = qdrant_mcp_client
        logger.info("✅ Qdrant MCP client initialized successfully")
        print("✅ Qdrant MCP Status: CLIENT INITIALIZED")
        return True
    except Exception as e:
        logger.error(f"❌ Qdrant MCP initialization error: {str(e)}")
        print("❌ Qdrant MCP Status: INITIALIZATION FAILED")
        return False
    finally:
        print("="*60)

# Test Qdrant MCP connection - now using the wrapper function
# The test_qdrant_mcp_connection function is imported from qdrant_mcp_wrapper

# Test Serper connection
print("\n" + "="*60)
print("🔄 SERPER CONNECTION STATUS")
print("="*60)

async def test_serper_connection():
    try:
        from server.serper_agent import query_google_search
        logger.info("🔗 Initializing Serper connection...")
        logger.info(f"Serper API endpoint: https://google.serper.dev/search")
        test_result = await query_google_search("test query", 1)
        if "❌" in test_result or "Error" in test_result:
            logger.error(f"❌ Serper test failed: {test_result}")
            print("❌ Serper Status: CONNECTION FAILED")
            return False
        else:
            logger.info("✅ Serper connection test successful")
            print("✅ Serper Status: CONNECTION SUCCESSFUL")
            return True
    except Exception as e:
        logger.error(f"❌ Serper connection error: {str(e)}")
        print("❌ Serper Status: INITIALIZATION FAILED")
        return False

# Fetch qdrant_find tool - now using the imported function from qdrant_mcp_wrapper
# The get_qdrant_tool function is imported from qdrant_mcp_wrapper

# Initialize MongoDB
mongo_client = MongoClient(MONGO_URL)
db = mongo_client[MONGO_DB]
history_collection = db[MONGO_COLLECTION]

# Session management
active_sessions = {}

# Utility Functions
async def ensure_qdrant_collection(collection: str) -> bool:
    logger.info(f"Checking existence of Qdrant collection '{collection}'")
    try:
        qdrant_tool = await get_qdrant_tool()
        await qdrant_tool.coroutine(query="test", collection_name=collection)
        logger.info(f"Collection {collection} exists")
        return True
    except Exception as e:
        logger.warning(f"Collection {collection} does not exist: {str(e)}")
        return False

def get_user_id(request: Request) -> str:
    client_ip = request.client.host
    if client_ip not in active_sessions:
        user_id_obj = ObjectId()
        active_sessions[client_ip] = {
            "user_id": str(user_id_obj),
            "current_chat_id": None,
            "last_accessed_history": None
        }
    return active_sessions[client_ip]["user_id"]

#def is_valid_object_id(chat_id: str) -> bool:
    #return bool(chat_id and isinstance(chat_id, str) and len(chat_id) == 24 and re.match(r'^[0-9a-fA-F]{24}$', chat_id))

def is_affirmative_response(query: str) -> bool:
    """
    Check if the user query is an affirmative response to a follow-up question.

    Args:
        query: User query string

    Returns:
        True if the query is an affirmative response, False otherwise
    """
    if not query or not query.strip():
        return False

    query_lower = query.strip().lower()

    # List of affirmative responses
    affirmative_words = [
        "yes", "y", "yeah", "yep", "yup", "sure", "ok", "okay", "alright",
        "go ahead", "go on", "continue", "proceed", "tell me more",
        "explain that", "explain more", "more details", "elaborate",
        "yes please", "sure thing", "absolutely", "definitely", "of course",
        "please do", "i'd like that", "sounds good", "that would be great",
        "tell me", "show me", "explain", "describe", "detail"
    ]

    # Check for exact matches
    if query_lower in affirmative_words:
        return True

    # Check for phrases that start with affirmative words
    affirmative_starters = ["yes,", "sure,", "ok,", "okay,", "please"]
    if any(query_lower.startswith(starter) for starter in affirmative_starters):
        return True

    # Check for common affirmative phrases
    affirmative_phrases = [
        "yes i would", "yes i'd", "i would like", "i'd like", "please tell me",
        "please explain", "please describe", "go ahead and", "yes go ahead",
        "sure go ahead", "that sounds", "sounds interesting"
    ]
    if any(phrase in query_lower for phrase in affirmative_phrases):
        return True

    return False
def get_latest_follow_up_from_redis(user_id: str, chat_id: str) -> str | None:
    """
    Retrieve the latest follow-up question from Redis.
    Always gets the most recent assistant message with a follow-up to enable chaining.

    Args:
        user_id: User identifier
        chat_id: Chat session identifier

    Returns:
        Latest follow-up question or None if not found
    """
    if not redis_client:
        logger.debug("Redis not available for follow-up retrieval")
        return None

    try:
        user_id_obj = memory_manager._validate_object_id(user_id, "user_id")
        chat_id_obj = memory_manager._validate_object_id(chat_id, "chat_id")
        session_key = f"session:{str(user_id_obj)}:{str(chat_id_obj)}"

        # Get ALL recent messages from Redis (more messages for better chain detection)
        messages = redis_client.lrange(session_key, -20, -1)  # Get last 20 messages

        if not messages:
            logger.debug(f"No messages found in Redis for session {session_key}")
            return None

        # Look for the MOST RECENT assistant message with a follow-up
        # This ensures we get the latest follow-up in the conversation chain
        latest_follow_up = None
        latest_timestamp = None

        for msg_str in reversed(messages):  # Start from the most recent
            try:
                msg = json.loads(msg_str)
                if (msg.get('role') == 'assistant' and
                    msg.get('metadata', {}).get('follow_up')):

                    # Get message timestamp for comparison
                    msg_timestamp = msg.get('timestamp')
                    if isinstance(msg_timestamp, str):
                        from datetime import datetime
                        try:
                            msg_timestamp = datetime.fromisoformat(msg_timestamp.replace('Z', '+00:00'))
                        except:
                            msg_timestamp = datetime.now()

                    # If this is the first follow-up found or it's more recent
                    if latest_timestamp is None or (msg_timestamp and msg_timestamp > latest_timestamp):
                        latest_follow_up = msg['metadata']['follow_up']
                        latest_timestamp = msg_timestamp
                        logger.debug(f"Found follow-up candidate: {latest_follow_up} at {msg_timestamp}")

                        # Since we're going in reverse chronological order,
                        # the first follow-up we find is the most recent
                        break

            except json.JSONDecodeError:
                continue

        if latest_follow_up:
            logger.info(f"🔗 Found latest follow-up in Redis: {latest_follow_up}")
            return latest_follow_up
        else:
            logger.debug("No follow-up questions found in Redis messages")
            return None

    except Exception as e:
        logger.error(f"Error retrieving follow-up from Redis: {e}")
        return None

def get_latest_follow_up_from_mongodb(user_id: str, chat_id: str) -> str | None:
    """
    Retrieve the latest follow-up question from MongoDB.
    Always gets the most recent assistant message with a follow-up to enable chaining.

    Args:
        user_id: User identifier
        chat_id: Chat session identifier

    Returns:
        Latest follow-up question or None if not found
    """
    try:
        user_id_obj = memory_manager._validate_object_id(user_id, "user_id")
        chat_id_obj = memory_manager._validate_object_id(chat_id, "chat_id")

        # Find the conversation document
        session_doc = history_collection.find_one({
            "$or": [{"chatId": chat_id_obj}, {"_id": chat_id_obj}],
            "user": user_id_obj
        })

        if not session_doc:
            logger.debug(f"No conversation found in MongoDB for chat_id {chat_id}, user {user_id}")
            return None

        messages = session_doc.get('messages', [])
        if not messages:
            logger.debug("No messages found in MongoDB conversation")
            return None

        # Look for the MOST RECENT assistant message with a follow-up
        # This ensures we get the latest follow-up in the conversation chain
        latest_follow_up = None
        latest_timestamp = None

        for msg in reversed(messages):  # Start from the most recent
            if (msg.get('role') == 'assistant' and
                msg.get('metadata', {}).get('follow_up')):

                # Get message timestamp for comparison
                msg_timestamp = msg.get('timestamp')
                if isinstance(msg_timestamp, str):
                    from datetime import datetime
                    try:
                        msg_timestamp = datetime.fromisoformat(msg_timestamp.replace('Z', '+00:00'))
                    except:
                        msg_timestamp = datetime.now()
                elif not isinstance(msg_timestamp, datetime):
                    msg_timestamp = datetime.now()

                # If this is the first follow-up found or it's more recent
                if latest_timestamp is None or (msg_timestamp and msg_timestamp > latest_timestamp):
                    latest_follow_up = msg['metadata']['follow_up']
                    latest_timestamp = msg_timestamp
                    logger.debug(f"Found follow-up candidate in MongoDB: {latest_follow_up} at {msg_timestamp}")

                    # Since we're going in reverse chronological order,
                    # the first follow-up we find is the most recent
                    break

        if latest_follow_up:
            logger.info(f"🔗 Found latest follow-up in MongoDB: {latest_follow_up}")
            return latest_follow_up
        else:
            logger.debug("No follow-up questions found in MongoDB messages")
            return None

    except Exception as e:
        logger.error(f"Error retrieving follow-up from MongoDB: {e}")
        return None

def get_latest_follow_up_question(user_id: str, chat_id: str) -> str | None:
    """
    Retrieve the latest follow-up question with Redis fallback to MongoDB.

    Args:
        user_id: User identifier
        chat_id: Chat session identifier

    Returns:
        Latest follow-up question or None if not found
    """
    # First try Redis
    follow_up = get_latest_follow_up_from_redis(user_id, chat_id)

    if follow_up:
        logger.info(f"Retrieved follow-up from Redis: {follow_up}")
        return follow_up

    # If Redis is empty or expired, try MongoDB
    logger.info("Redis session expired or empty, falling back to MongoDB")
    follow_up = get_latest_follow_up_from_mongodb(user_id, chat_id)

    if follow_up:
        logger.info(f"Retrieved follow-up from MongoDB: {follow_up}")

        # Restore messages from MongoDB to Redis for future queries
        try:
            restore_session_from_mongodb(user_id, chat_id)
        except Exception as e:
            logger.error(f"Error restoring session from MongoDB: {e}")

        return follow_up

    logger.debug("No follow-up questions found in either Redis or MongoDB")
    return None

def restore_session_from_mongodb(user_id: str, chat_id: str):
    """
    Restore all messages from MongoDB to Redis when session expires.

    Args:
        user_id: User identifier
        chat_id: Chat session identifier
    """
    try:
        user_id_obj = memory_manager._validate_object_id(user_id, "user_id")
        chat_id_obj = memory_manager._validate_object_id(chat_id, "chat_id")

        # Get all messages from MongoDB
        mongodb_messages = get_mongodb_history_for_session(chat_id, limit=50)  # Get more messages for context

        if mongodb_messages:
            logger.info(f"Restoring {len(mongodb_messages)} messages from MongoDB to Redis")

            # Store each message in Redis
            for msg in mongodb_messages:
                store_session_message(str(user_id_obj), str(chat_id_obj), msg)

            logger.info(f"Successfully restored session from MongoDB to Redis for chat_id={chat_id}")
        else:
            logger.debug("No messages found in MongoDB to restore")

    except Exception as e:
        logger.error(f"Error restoring session from MongoDB: {e}")

def mark_follow_up_as_used(user_id: str, chat_id: str, follow_up_question: str):
    """
    Mark a follow-up question as used by adding a timestamp to prevent reuse.

    Args:
        user_id: User identifier
        chat_id: Chat session identifier
        follow_up_question: The follow-up question that was used
    """
    try:
        user_id_obj = memory_manager._validate_object_id(user_id, "user_id")
        chat_id_obj = memory_manager._validate_object_id(chat_id, "chat_id")

        # Store a marker message to indicate this follow-up was used
        marker_message = {
            "id": str(ObjectId()),
            "role": "system",
            "content": f"[FOLLOW_UP_USED] {follow_up_question}",
            "timestamp": datetime.now(),
            "metadata": {
                "chat_id": str(chat_id_obj),
                "user_id": str(user_id_obj),
                "type": "follow_up_marker",
                "used_follow_up": follow_up_question,
                "used_at": datetime.now().isoformat()
            }
        }

        # Store in Redis if available
        if redis_client:
            session_key = f"session:{str(user_id_obj)}:{str(chat_id_obj)}"
            redis_client.rpush(session_key, json.dumps(marker_message))
            redis_client.expire(session_key, SESSION_TTL)
            logger.debug(f"Marked follow-up as used in Redis: {follow_up_question}")

        logger.info(f"🔒 Marked follow-up as used: {follow_up_question}")

    except Exception as e:
        logger.error(f"Error marking follow-up as used: {e}")

def is_follow_up_already_used(user_id: str, chat_id: str, follow_up_question: str) -> bool:
    """
    Check if a follow-up question has already been used.

    Args:
        user_id: User identifier
        chat_id: Chat session identifier
        follow_up_question: The follow-up question to check

    Returns:
        True if the follow-up has been used, False otherwise
    """
    if not redis_client:
        return False

    try:
        user_id_obj = memory_manager._validate_object_id(user_id, "user_id")
        chat_id_obj = memory_manager._validate_object_id(chat_id, "chat_id")
        session_key = f"session:{str(user_id_obj)}:{str(chat_id_obj)}"

        # Get recent messages from Redis
        messages = redis_client.lrange(session_key, -20, -1)

        for msg_str in messages:
            try:
                msg = json.loads(msg_str)
                if (msg.get('role') == 'system' and
                    msg.get('metadata', {}).get('type') == 'follow_up_marker' and
                    msg.get('metadata', {}).get('used_follow_up') == follow_up_question):
                    logger.debug(f"Follow-up already used: {follow_up_question}")
                    return True
            except json.JSONDecodeError:
                continue

        return False

    except Exception as e:
        logger.error(f"Error checking if follow-up is used: {e}")
        return False

def get_latest_unused_follow_up_from_redis(user_id: str, chat_id: str) -> str | None:
    """
    Retrieve the latest UNUSED follow-up question from Redis.

    Args:
        user_id: User identifier
        chat_id: Chat session identifier

    Returns:
        Latest unused follow-up question or None if not found
    """
    if not redis_client:
        logger.debug("Redis not available for follow-up retrieval")
        return None

    try:
        user_id_obj = memory_manager._validate_object_id(user_id, "user_id")
        chat_id_obj = memory_manager._validate_object_id(chat_id, "chat_id")
        session_key = f"session:{str(user_id_obj)}:{str(chat_id_obj)}"

        # Get ALL recent messages from Redis
        messages = redis_client.lrange(session_key, -30, -1)  # Get more messages

        if not messages:
            logger.debug(f"No messages found in Redis for session {session_key}")
            return None

        # Look for the MOST RECENT assistant message with an UNUSED follow-up
        for msg_str in reversed(messages):  # Start from the most recent
            try:
                msg = json.loads(msg_str)
                if (msg.get('role') == 'assistant' and
                    msg.get('metadata', {}).get('follow_up')):

                    follow_up = msg['metadata']['follow_up']

                    # Check if this follow-up has already been used
                    if not is_follow_up_already_used(user_id, chat_id, follow_up):
                        logger.info(f"🔗 Found latest unused follow-up in Redis: {follow_up}")
                        return follow_up
                    else:
                        logger.debug(f"Skipping used follow-up: {follow_up}")

            except json.JSONDecodeError:
                continue

        logger.debug("No unused follow-up questions found in Redis messages")
        return None

    except Exception as e:
        logger.error(f"Error retrieving unused follow-up from Redis: {e}")
        return None

def handle_follow_up_continuation(query: str, user_id: str, chat_id: str) -> tuple[bool, str | None]:
    """
    Handle follow-up question continuation logic with chaining support.

    Args:
        query: User query
        user_id: User identifier
        chat_id: Chat session identifier

    Returns:
        Tuple of (is_continuation, follow_up_question)
        - is_continuation: True if this is a follow-up continuation
        - follow_up_question: The follow-up question to use as the new query
    """
    # Check if the query is an affirmative response
    if not is_affirmative_response(query):
        logger.debug(f"Query '{query}' is not an affirmative response")
        return False, None

    # Get the latest UNUSED follow-up question (enables chaining)
    follow_up_question = get_latest_unused_follow_up_from_redis(user_id, chat_id)

    # If Redis doesn't have unused follow-up, try MongoDB
    if not follow_up_question:
        follow_up_question = get_latest_follow_up_from_mongodb(user_id, chat_id)

        # If found in MongoDB, restore session to Redis
        if follow_up_question:
            try:
                restore_session_from_mongodb(user_id, chat_id)
            except Exception as e:
                logger.error(f"Error restoring session from MongoDB: {e}")

    if follow_up_question:
        # Mark this follow-up as used to prevent reuse
        mark_follow_up_as_used(user_id, chat_id, follow_up_question)

        logger.info(f"🔄 Follow-up continuation detected! Original query: '{query}' -> Follow-up: '{follow_up_question}'")
        return True, follow_up_question
    else:
        logger.debug("No unused follow-up question found for continuation")
        return False, None

def is_valid_object_id(id_str: str) -> bool:
    try:
        ObjectId(id_str)
        return True
    except:
        return False
    
def store_session_message(user_id: str, chat_id: str, message: dict):
    if redis_client is None:
        logger.debug("Redis not available, skipping session message storage")
        return
    try:
        user_id_obj = memory_manager._validate_object_id(user_id, "user_id")
        chat_id_obj = memory_manager._validate_object_id(chat_id, "chat_id")

        session_key = f"session:{str(user_id_obj)}:{str(chat_id_obj)}"
        
        message_copy = message.copy()
        if isinstance(message_copy.get('timestamp'), datetime):
            message_copy['timestamp'] = message_copy['timestamp'].isoformat()
        if 'timestamp' not in message_copy:
            message_copy['timestamp'] = datetime.now().isoformat()
        if isinstance(message_copy.get('id'), ObjectId):
            message_copy['id'] = str(message_copy['id'])
        if 'metadata' in message_copy:
            if isinstance(message_copy['metadata'].get('chat_id'), ObjectId):
                message_copy['metadata']['chat_id'] = str(message_copy['metadata']['chat_id'])
            if isinstance(message_copy['metadata'].get('user_id'), ObjectId):
                message_copy['metadata']['user_id'] = str(message_copy['metadata']['user_id'])
        
        # If it's an assistant message, extract and store follow-up in metadata
        if message_copy.get('role') == 'assistant':
            answer, follow_up = memory_manager.extract_follow_up(message_copy['content'])
            message_copy['content'] = answer
            #message_copy['metadata']['follow_up'] = follow_up if follow_up else None
            # Ensure metadata exists
            if 'metadata' not in message_copy:
               message_copy['metadata'] = {}
            # Store follow-up question under assistant metadata
            message_copy['metadata']['follow_up'] = follow_up   #if follow_up else None
            #logger.debug(f"Stored follow-up in Redis assistant metadata: {follow_up is not None}")
            logger.debug(f"Storing assistant message in Redis with follow_up: {follow_up}")

        redis_client.rpush(session_key, json.dumps(message_copy))
        redis_client.expire(session_key, SESSION_TTL)
        redis_client.ltrim(session_key, -10, -1)
        logger.info(f"Stored message in AWS Redis for session {session_key}")
    except redis.AuthenticationError as e:
        logger.error(f"AWS Redis authentication error during store_session_message: {str(e)}")
    except redis.ConnectionError as e:
        logger.error(f"AWS Redis connection error during store_session_message: {str(e)}")
    except Exception as e:
        logger.error(f"Error storing message in AWS Redis: {str(e)}")


def store_session_metadata(user_id: str, chat_id: str, metadata: dict):
    if redis_client is None:
        logger.debug("Redis not available, skipping session metadata storage")
        return
    try:
        #user_id_obj = ObjectId(user_id)
        #chat_id_obj = ObjectId(chat_id)

        user_id_obj = memory_manager._validate_object_id(user_id, "user_id")
        chat_id_obj = memory_manager._validate_object_id(chat_id, "chat_id")

        metadata_key = f"session_meta:{user_id_obj}:{chat_id_obj}"
        metadata_with_timestamp = metadata.copy()
        metadata_with_timestamp['updatedAt'] = datetime.now()
        for key, value in metadata_with_timestamp.items():
            redis_client.hset(metadata_key, key, str(value))
        redis_client.expire(metadata_key, SESSION_TTL)
        logger.info(f"Stored session metadata in AWS Redis for {metadata_key}")
    except redis.ConnectionError as e:
        logger.error(f"AWS Redis connection error during store_session_metadata: {str(e)}")
    except Exception as e:
        logger.error(f"Error storing session metadata in AWS Redis: {str(e)}")

def get_session_metadata(user_id: str, chat_id: str) -> dict:
    if redis_client is None:
        logger.debug("Redis not available, returning empty metadata")
        return {}
    try:
        user_id_obj = memory_manager._validate_object_id(user_id, "user_id")
        chat_id_obj = memory_manager._validate_object_id(chat_id, "chat_id")

        metadata_key = f"session_meta:{user_id_obj}:{chat_id_obj}"
        metadata = redis_client.hgetall(metadata_key)
        if metadata:
            redis_client.expire(metadata_key, SESSION_TTL)
            logger.debug(f"Retrieved session metadata from AWS Redis for {metadata_key}")
        return metadata
    except redis.ConnectionError as e:
        logger.error(f"AWS Redis connection error during get_session_metadata: {str(e)}")
        return {}
    except Exception as e:
        logger.error(f"Error retrieving session metadata from AWS Redis: {str(e)}")
        return {}

def get_session_messages(user_id: str, chat_id: str, limit: int = 10) -> List[dict]:
    if redis_client is None:
        logger.debug("Redis not available, falling back to MongoDB")
        return get_mongodb_history_for_session(chat_id_obj, limit)
    try:
        user_id_obj = memory_manager._validate_object_id(user_id, "user_id")
        chat_id_obj = memory_manager._validate_object_id(chat_id, "chat_id")

        session_key = f"session:{user_id_obj}:{chat_id_obj}"
        messages = redis_client.lrange(session_key, -limit, -1)
        if messages:
            redis_client.expire(session_key, SESSION_TTL)
            session_messages = []
            for msg in messages:
                parsed_msg = json.loads(msg)
                parsed_msg = json.loads(msg)
                # Accept messages that either have matching chat_id or no chat_id in metadata
                # This handles both old messages (without chat_id) and new messages (with chat_id)
                msg_chat_id = parsed_msg.get('metadata', {}).get('chat_id')
                if msg_chat_id == chat_id or msg_chat_id is None:
                    session_messages.append(parsed_msg)
                else:
                    logger.warning(f"Cross-session contamination detected in {session_key}: expected {chat_id}, got {msg_chat_id}")
            logger.debug(f"Retrieved {len(session_messages)} messages from Redis for session {session_key}")
            return session_messages
        logger.info(f"Redis session expired for {chat_id}, falling back to MongoDB")
        mongodb_messages = get_mongodb_history_for_session(chat_id, limit)
        if mongodb_messages and redis_client:
            for msg in mongodb_messages:
                store_session_message(user_id, chat_id, msg)
            logger.info(f"Restored {len(mongodb_messages)} messages to Redis from MongoDB")
        return mongodb_messages
    except redis.RedisError as e:
        logger.error(f"Redis error during get_session_messages: {str(e)}")
        return get_mongodb_history_for_session(chat_id, limit)

def get_mongodb_history_for_session(chat_id: str, limit: int = 10) -> List[dict]:
    try:
        chat_id_obj = memory_manager._validate_object_id(chat_id, "chat_id")

        session_doc = history_collection.find_one({"$or": [{"chatId": chat_id_obj}, {"_id": chat_id_obj}]})
        if not session_doc:
            return []
        messages = session_doc.get('messages', [])
        return messages[-limit:] if len(messages) > limit else messages
    except Exception as e:
        logger.error(f"Error retrieving MongoDB history for session {chat_id}: {str(e)}")
        return []

def clear_session(user_id: str, chat_id: str):
    if redis_client is None:
        logger.debug("Redis not available, skipping session cleanup")
        return
    try:
        user_id_obj = memory_manager._validate_object_id(user_id, "user_id")
        chat_id_obj = memory_manager._validate_object_id(chat_id, "chat_id")

        session_key = f"session:{user_id_obj}:{chat_id_obj}"
        metadata_key = f"session_meta:{user_id_obj}:{chat_id_obj}"
        deleted_count = redis_client.delete(session_key, metadata_key)
        logger.info(f"Cleared {deleted_count} keys from AWS Redis: {session_key} and {metadata_key}")
    except redis.AuthenticationError as e:
        logger.error(f"AWS Redis authentication error during clear_session: {str(e)}")
    except redis.ConnectionError as e:
        logger.error(f"AWS Redis connection error during clear_session: {str(e)}")
    except Exception as e:
        logger.error(f"Error clearing session from AWS Redis: {str(e)}")

def get_session_history(chat_id: str) -> Dict:
    try:
        #chat_id_obj = ObjectId(chat_id)
        chat_id_obj = memory_manager._validate_object_id(chat_id, "chat_id")

        session_doc = history_collection.find_one({"$or": [{"chatId": chat_id_obj}, {"_id": chat_id_obj}]})
        if not session_doc:
            raise HTTPException(status_code=404, detail=f"Chat ID {chat_id_obj} not found in MongoDB.")
        processed_doc = {
            '_id': str(session_doc.get('_id', None)),
            'user': str(session_doc.get('user', None)) if session_doc.get('user') else None,
            'chatId': str(session_doc.get('chatId', None)) if session_doc.get('chatId') else None,
            'createdAt': str(session_doc.get('createdAt', None)) if session_doc.get('createdAt') else None,
            'updatedAt': str(session_doc.get('updatedAt', None)) if session_doc.get('updatedAt') else None,
            'title': session_doc.get('title', None),
            'analytics': {
                'lastViewedAt': str(session_doc['analytics'].get('lastViewedAt', None)) if session_doc.get('analytics', {}).get('lastViewedAt') else None,
                'views': session_doc.get('analytics', {}).get('views', 0),
                'exportCount': session_doc.get('analytics', {}).get('exportCount', 0),
                'searchCount': session_doc.get('analytics', {}).get('searchCount', 0)
            } if session_doc.get('analytics') else {'lastViewedAt': None, 'views': 0, 'exportCount': 0, 'searchCount': 0},
            'messages': [
                {
                    'id': str(msg.get('id', None)),
                    'role': msg.get('role', None),
                    'content': msg.get('content', None),
                    'timestamp': str(msg.get('timestamp', None)) if msg.get('timestamp') else None,
                    'metadata': msg.get('metadata', {}).copy() if isinstance(msg.get('metadata'), dict) else {}
                } for msg in session_doc.get('messages', []) if isinstance(msg, dict)
            ],
            'summary': session_doc.get('summary', None),
            'metadata': session_doc.get('metadata', {
                'totalMessages': 0,
                'totalTokensUsed': 0,
                'averageResponseTime': 0,
                'conversationDuration': 0,
                'topics': [],
                'sentiment': 'neutral',
                'complexity': 'simple'
            }),
            'status': session_doc.get('status', 'active'),
            'tags': session_doc.get('tags', []),
            'isBookmarked': session_doc.get('isBookmarked', False),
            'isShared': session_doc.get('isShared', True),
            'sharedWith': session_doc.get('sharedWith', []),
            '__v': session_doc.get('__v', 0)
        }
        if not any(processed_doc.values()):
            raise HTTPException(status_code=500, detail=f"Processed chat history for ID {chat_id} is empty.")
        return processed_doc
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid chat ID format: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching chat history: {str(e)}")

def append_to_chat_history(chat_id: str, query: str, answer: str, user_id: str,
                          sources: List[str] = None, documents: List[str] = None,  source_urls: List[str] = None,
                          mode: str = None, host: str = None, model: str = None, follow_up: Optional[str] = None,
                          skip_redis: bool = False):
    """
    Append messages to chat history in MongoDB and optionally Redis.

    Args:
        chat_id: Chat session identifier
        query: User query
        response: System response
        user_id: User identifier
        sources: List of source names
        documents: List of Collections and Files 
        source_urls: List of source URLs
        mode: Query mode (rag, agentic, web)
        host: LLM host
        model: LLM model
        skip_redis: If True, skip storing in Redis (to prevent duplicates)
    """
    try:
        #chat_id_obj = ObjectId(chat_id)
        user_id_obj = memory_manager._validate_object_id(user_id, "user_id")
        chat_id_obj = memory_manager._validate_object_id(chat_id, "chat_id")
        #current_time = datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
        current_time = datetime.now()

        # Extract follow-up question from the answer
        answer, follow_up = memory_manager.extract_follow_up(answer)
        logger.debug(f"Extracted answer: {answer}, follow_up: {follow_up}")

        user_message = {
            "id": str(ObjectId()),
            "role": "user",
            "content": query,
            "timestamp": current_time,
            "metadata": {
                "chat_id": chat_id_obj,
                "mode": mode,
                "host": host,
                "model": model
            }
        }
        assistant_message = {
            "id": str(ObjectId()),
            "role": "assistant",
            "content": answer,
            "timestamp": current_time,
            "metadata": {
                "chat_id": chat_id_obj,
                "sources": sources or [],
                "documents": documents or [],
                "source_urls": source_urls or [],
                "mode": mode,
                "host": host,
                "model": model,
                "follow_up": follow_up if follow_up else None
            }
        }

        logger.debug(f"Prepared assistant message with metadata: {assistant_message['metadata']}")
        # Only store in Redis if not skipped (to prevent duplicates with memory_manager.store_interaction)

        if not skip_redis and redis_client:
            session_key = f"session:{user_id_obj}:{chat_id_obj}"

            # Check if this query already exists in Redis to prevent duplicates
            existing_messages = redis_client.lrange(session_key, 0, -1)
            existing_messages = [json.loads(msg) for msg in existing_messages] if existing_messages else []

            # Check if this exact query already exists
            query_exists = any(
                msg.get('role') == 'user' and
                msg.get('content') == query and
                msg.get('metadata', {}).get('chat_id') == chat_id
                for msg in existing_messages
            )

            if query_exists:
                logger.info(f"Query '{query}' already exists in Redis for chat_id={chat_id}. Skipping duplicate storage.")
            else:
                # Store messages in Redis
                store_session_message(user_id_obj, chat_id_obj, user_message)
                store_session_message(user_id_obj, chat_id_obj, assistant_message)
                logger.info(f"Stored new messages in Redis for chat_id={chat_id}")

        # Store in MongoDB using MemoryManager
        metadata = {
            "mode": mode,
            "host": host,
            "model": model,
            "sources": sources or [],
            "source_urls": source_urls or [],
            "follow_up": follow_up if follow_up else None
        }
        memory_manager.store_interaction(query, answer, chat_id_obj, user_id_obj, metadata)
        logger.info(f"Stored interaction in MongoDB via MemoryManager for chat_id={chat_id}")

    except Exception as e:
        logger.error(f"Error appending to chat history: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error appending to chat history: {str(e)}")
    
def create_new_chat(user_id: str) -> str:
    try:
        #current_time = datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
        current_time = datetime.now()
        chat_id = str(ObjectId())
        user_id_obj = memory_manager._validate_object_id(user_id, "user_id")
        chat_id_obj = memory_manager._validate_object_id(chat_id, "chat_id")

        history_collection.insert_one({
            "_id": ObjectId(chat_id),
            "user": user_id_obj,
            "chatId": chat_id_obj,
            "createdAt": current_time,
            "updatedAt": current_time,
            "title": "New Chat",
            "analytics": {"agent": "WEB", "views": 1, "exportCount": 0, "searchCount": 0},
            "messages": [],
            "summary": "",
            "metadata": {
                "totalMessages": 0,
                "totalTokensUsed": 0,
                "averageResponseTime": 0,
                "conversationDuration": 0,
                "topics": [],
                "sentiment": "neutral",
                "complexity": "simple"
            },
            "status": "active",
            "tags": [],
            "isBookmarked": False,
            "isShared": True,
            "sharedWith": []
        })
        return chat_id
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating new chat: {str(e)}")

def validate_model(host: str, model: str, api_key: str) -> bool:
    try:
        headers = {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}
        if host == "spaarxsenseaifabric":
            if model != "deepseek-r1:1.5b":
                logger.error(f"Only deepseek-r1:1.5b is supported for {host}, got: {model}")
                return False
            if not api_key:
                logger.error("API key is empty for spaarxsenseaifabric")
                return False
            if not re.match(r'^[a-zA-Z0-9]+$', api_key):
                logger.error(f"Invalid API key format for spaarxsenseaifabric: {api_key}")
                return False
            return True
        elif host == "groq":
            response = requests.get("https://api.groq.com/openai/v1/models", headers=headers)
            response.raise_for_status()
            models = [m["id"] for m in response.json().get("data", [])]
            return model in models
        elif host == "google":
            response = requests.get("https://generativelanguage.googleapis.com/v1/models", headers={"x-goog-api-key": api_key})
            response.raise_for_status()
            models = [m["name"].split("/")[-1] for m in response.json().get("models", [])]
            return model in models
        elif host == "openai":
            response = requests.get("https://api.openai.com/v1/models", headers=headers)
            response.raise_for_status()
            models = [m["id"] for m in response.json().get("data", [])]
            return model in models
        elif host == "meta" or host == "microsoft":
            return bool(re.match(r'^[a-zA-Z0-9\-]+/[a-zA-Z0-9\-]+$', model))
        elif host == "anthropic":
            return model.startswith("claude-")
        return False
    except requests.exceptions.RequestException as e:
        logger.error(f"Error validating model for {host}: {str(e)}")
        return False

# Request/Response Models
class FeedbackRequest(BaseModel):
    feedback_type: str
    feedback_value: Optional[Any] = None
    timestamp: Optional[str] = None

class FeedbackResponse(BaseModel):
    status: str
    message: Optional[str] = None
    feedback: Optional[dict] = None

class QueryRequest(BaseModel):
    query: str
    chat_id: Optional[str] = None
    collections: List[str] = Field(default_factory=list)
    mode: Optional[str] = None
    host: Optional[str] = None
    api_key: Optional[str] = None
    model: Optional[str] = None
    user_id:str
    #data_source_config: Optional[Dict] = None
    #server_id: Optional[str] = None 
    #user_id: Optional[str] = None  # Add user_id parameter for dynamic_mcp mode

class RagQueryRequest(BaseModel):
    query: str 
    chat_id: Optional[str] = None
    collections: List[str] = Field(default_factory=list)
    mode: Optional[str] = None
    host: Optional[str] = None
    api_key: Optional[str] = None
    model: Optional[str] = None
    user_id: str


class QueryResponse(BaseModel):
    answer: str
    source: str
    source_urls: List[str] = Field(default_factory=list)
    documents: List[dict] = Field(default_factory=list)
    # chat_id: str
    chat_id: Optional[str] = None

class HistoryResponse(BaseModel):
    pass

class NewChatResponse(BaseModel):
    chat_id: str

class MCPServerType(str, Enum):
    CUSTOM = "custom"
    PUBLIC = "public"

class CustomCategory(str, Enum):
    CLOUD_STORAGE = "cloud_storage"
    DATABASES = "databases"
    DEVOPS_TOOLS = "devops_tools"
    GIT = "git"
    LOCALLY_AVAILABLE = "locally_available"

class CloudStorageService(str, Enum):
    AWS_S3 = "aws_s3"
    GCS_DRIVE = "gcs_drive"
    GOOGLE_DRIVE = "google_drive"
    MICROSOFT_SHAREPOINT = "microsoft_sharepoint"
    ONEDRIVE = "onedrive"
    AZURE_BLOB = "azure_blob"

class DatabaseService(str, Enum):
    POSTGRES = "postgres"
    MYSQL = "mysql"
    MONGODB = "mongodb"
    REDIS = "redis"
    SQL_SERVER = "sql_server"

class DevOpsService(str, Enum):
    JIRA = "jira"
    AZURE_DEVOPS = "azure_devops"

class GitService(str, Enum):
    GITHUB = "github"
    BITBUCKET = "bitbucket"
    AZURE_REPOS = "azure_repos"

class PublicService(str, Enum):
    AIRBNB = "airbnb"
    WEATHER = "weather"
    NEWS = "news"
    GOOGLE = "google"
    BRAVE_SEARCH = "brave_search"
    GOOGLE_MAPS = "google_maps"

class AWSS3Credentials(BaseModel):
    aws_access_key_id: str
    aws_secret_access_key: str
    bucket_name: str
    region: str
    session_token: Optional[str] = None
    gemini_api_key: str

class GCSCredentials(BaseModel):
    service_account_key_json: str
    bucket_name: Optional[str] = None
    project_id: str
    gemini_api_key: str

class GoogleDriveCredentials(BaseModel):
    #client_id: str
    #client_secret: str
    #refresh_token: str
    url: str
    gemini_api_key: str

class SharePointCredentials(BaseModel):
    tenant_id: str
    client_id: str
    client_secret: str
    sharepoint_site_url: str
    resource_drive_id: Optional[str] = None
    gemini_api_key: str
    site_id: str

class OneDriveCredentials(BaseModel):
    tenant_id: str
    client_id: str
    client_secret: str
    refresh_token: str
    drive_id: str
    gemini_api_key: str

class AzureBlobCredentials(BaseModel):
    storage_account_name: str
    access_key: str
    container_name: str
    endpoint_url: Optional[str] = None
    gemini_api_key: str

class DatabaseCredentials(BaseModel):
    host: str
    port: int
    username: str
    password: str
    database_name: str
    connection_string: Optional[str] = None

class GitCredentials(BaseModel):
    username: str
    token: str
    repository_url: Optional[str] = None
    organization: Optional[str] = None

class DevOpsCredentials(BaseModel):
    api_url: str
    username: str
    api_token: str
    project_key: Optional[str] = None

class PublicCredentials(BaseModel):
    url: str
    api_key: str

class CreateMCPServerRequest(BaseModel):
    server_type: MCPServerType
    category: Optional[CustomCategory] = None
    service: Optional[str] = None
    #credentials: Dict[str, Any] = None
    credentials: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Flexible credentials object - add any key-value pairs required for your MCP Server",
        example={"api_key": "your_api_key_here"}
    )
    server_name: str
    description: Optional[str] = None
    user_id: Optional[str] = None

class MCPServerInfo(BaseModel):
    server_id: str
    server_name: str
    server_type: MCPServerType
    category: Optional[str] = None
    service: Optional[str] = None
    status: str
    createdAt: str
    last_used: Optional[str] = None
    user_id: Optional[str] = None

class CreateMCPServerResponse(BaseModel):
    success: bool
    server_id: str
    server_info: MCPServerInfo
    message: str

class DeleteMCPServerResponse(BaseModel):
    success: bool
    message: str

# Initialize Multi-Agent RAG components
print("\n" + "="*60)
print("🤖 MULTI-AGENT RAG INITIALIZATION")
print("="*60)

try:
    memory_manager = get_memory_manager(
        redis_client=redis_client,
        mongo_client=mongo_client,
        mongo_db=db,
        mongo_collection=history_collection
    )
    logger.info("✅ Memory manager initialized successfully")
except Exception as e:
    logger.error(f"❌ Error initializing memory manager: {e}")
    memory_manager = get_memory_manager(
        redis_client=redis_client,
        mongo_client=None,
        mongo_db=None,
        mongo_collection=None
    )

planning_manager = get_planning_manager()
generative_model = get_generative_model()
aggregator_agent = get_aggregator_agent(memory_manager, planning_manager)

logger.info("✅ Multi-Agent RAG components initialized")
print("✅ Multi-Agent RAG Status: INITIALIZED")
print("="*60)
    
# Multi-Agent RAG Processing Function
async def process_multi_agent_query(query: str, mode: str, collections: List[str],
                                   host: str, model: str, api_key: str,
                                   chat_id: str, user_id: str, history: str = "") -> Dict[str, Any]:
    try:
        logger.info(f"🤖 Processing Multi-Agent query: '{query}' (mode: {mode})")
        # 🔄 Check if user accepted last follow-up
        #last_followup = get_last_followup(user_id, chat_id)
        #if query.strip().lower() in ["yes", "y", "sure", "ok", "okay", 'ya', 'sure', 'yeah', 'yes please', 'tell me more', 'go ahead', 'explain that'] and last_followup:
            #logger.info(f"User accepted follow-up, replacing query with: {last_followup}")
            #query = last_followup

        plan = planning_manager.create_execution_plan(query, mode, collections)
        logger.info(f"📋 Execution plan created: {plan['mode']} mode, {len(plan['execution_steps'])} steps")

        try:
            memory_context = memory_manager.get_context(chat_id, user_id, limit=10)
            logger.info(f"🧠 Memory context retrieved: {len(memory_context)} characters")
        except Exception as e:
            logger.error(f"❌ Error getting memory context: {e}")
            memory_context = ""
        
         # Get feedback context for improvement
        try:
            feedback_context = get_feedback_context_for_improvement(user_id, chat_id)
            if feedback_context:
                logger.info(f"📝 Feedback context retrieved for improvement: {len(feedback_context)} characters")
            else:
                logger.debug("No feedback context available")
        except Exception as e:
            logger.error(f"❌ Error getting feedback context: {e}")
            feedback_context = ""

        # For dynamic_mcp mode, check for dynamic agents (ONLY for dynamic_mcp mode)
        if mode == "dynamic_mcp":
            agents = get_all_dynamic_agents()
            user_agent = next((agent for agent in agents if agent.get('user_id') == user_id), None)

            if not user_agent:
                logger.warning(f"Dynamic MCP mode requested but no dynamic agent found for user_id={user_id}")
                return {
                    "answer": "No dynamic MCP server found for this user. Please create a dynamic MCP server first.",
                    "source": "error",
                    "documents": [],
                    "source_urls": [],
                    "score": 0.0,
                    "decision": "no_dynamic_agent",
                    "decision_reason": "Dynamic MCP mode requested but no dynamic agent available",
                    "collections_searched": [],
                    "final_context": [],
                    "plan": plan,
                    "reasoning": {"final_reasoning": "No dynamic agent available"},
                    "mode": mode
                }

            logger.info(f"Using dynamic agent for dynamic_mcp mode, user_id={user_id}")
            try:
                dynamic_result = await query_user_dynamic_agent(user_id, query, mode)
                if dynamic_result.get("success", False):
                    logger.info(f"Dynamic agent query successful in dynamic_mcp mode")
                    answer = dynamic_result.get("result", "No result from dynamic MCP server")
                    source_urls = dynamic_result.get("source_urls", [])

                    # Store chat history for dynamic_mcp mode (same as other modes)
                    interaction_metadata = {
                        "mode": mode,
                        "host": host,
                        "model": model,
                        "decision": "dynamic_mcp_success",
                        "score": 1.0,
                        "plan_id": plan["plan_id"]
                    }

                    memory_manager.store_interaction(
                        query=query,
                        response=answer,
                        chat_id=chat_id,
                        user_id=user_id,
                        metadata=interaction_metadata
                    )
                    logger.info(f"Stored dynamic_mcp interaction in chat history for chat_id={chat_id}")

                    return {
                        "answer": answer,
                        "source": "dynamic_mcp",
                        "documents": [],
                        "source_urls": source_urls,
                        "score": 1.0,
                        "decision": "dynamic_mcp_success",
                        "decision_reason": "Successfully used dynamic MCP server",
                        "collections_searched": [],
                        "final_context": [answer],
                        "plan": plan,
                        "reasoning": {"final_reasoning": "Dynamic MCP server response"},
                        "mode": mode
                    }
                else:
                    error_type = dynamic_result.get("error", "Unknown error")
                    result_msg = dynamic_result.get("result", "")

                    # Handle different error types with appropriate fallback
                    if error_type == "no_agents":
                        logger.info(f"No dynamic agents found for user {user_id}")
                        error_answer = "Sorry, you haven't created any dynamic MCP servers yet. To process your query using dynamic MCP mode, you need to create a dynamic MCP server first. You can create servers for news, weather, accommodations, and more using the /create_mcp_server endpoint."

                        # Store chat history for error case
                        interaction_metadata = {
                            "mode": mode,
                            "host": host,
                            "model": model,
                            "decision": "dynamic_mcp_no_servers",
                            "score": 0.0,
                            "plan_id": plan["plan_id"]
                        }

                        memory_manager.store_interaction(
                            query=query,
                            response=error_answer,
                            chat_id=chat_id,
                            user_id=user_id,
                            metadata=interaction_metadata
                        )
                        logger.info(f"Stored dynamic_mcp error interaction in chat history for chat_id={chat_id}")

                        # Return specific dynamic_mcp error message, do not fall back to RAG
                        return {
                            "answer": error_answer,
                            "source": "dynamic_mcp_no_servers",
                            "documents": [],
                            "source_urls": [],
                            "score": 0.0,
                            "decision": "dynamic_mcp_no_servers",
                            "decision_reason": "No dynamic MCP servers created for user",
                            "collections_searched": [],
                            "final_context": [],
                            "plan": plan,
                            "reasoning": {"final_reasoning": "User needs to create dynamic MCP servers first"},
                            "mode": mode,
                            "suggestion": "Create a dynamic MCP server using the /create_mcp_server endpoint"
                        }
                    elif error_type == "out_of_context":
                        logger.info(f"Query out of context for available services")

                        # Store chat history for out of context error
                        interaction_metadata = {
                            "mode": mode,
                            "host": host,
                            "model": model,
                            "decision": "dynamic_mcp_out_of_context",
                            "score": 0.0,
                            "plan_id": plan["plan_id"]
                        }

                        memory_manager.store_interaction(
                            query=query,
                            response=result_msg,
                            chat_id=chat_id,
                            user_id=user_id,
                            metadata=interaction_metadata
                        )
                        logger.info(f"Stored dynamic_mcp out_of_context interaction in chat history for chat_id={chat_id}")

                        return {
                            "answer": result_msg,
                            "source": "dynamic_mcp_context_error",
                            "documents": [],
                            "source_urls": [],
                            "score": 0.0,
                            "decision": "dynamic_mcp_out_of_context",
                            "decision_reason": "Query outside available service context",
                            "collections_searched": [],
                            "final_context": [],
                            "plan": plan,
                            "reasoning": {"final_reasoning": "Query not suitable for available MCP services"},
                            "mode": mode,
                            "available_services": dynamic_result.get("available_services", [])
                        }
                    else:
                        logger.error(f"Dynamic agent query failed: {error_type}")
                        error_answer = result_msg or f"Dynamic MCP server error: {error_type}"

                        # Store chat history for general error case
                        interaction_metadata = {
                            "mode": mode,
                            "host": host,
                            "model": model,
                            "decision": "dynamic_mcp_failed",
                            "score": 0.0,
                            "plan_id": plan["plan_id"]
                        }

                        memory_manager.store_interaction(
                            query=query,
                            response=error_answer,
                            chat_id=chat_id,
                            user_id=user_id,
                            metadata=interaction_metadata
                        )
                        logger.info(f"Stored dynamic_mcp error interaction in chat history for chat_id={chat_id}")

                        return {
                            "answer": error_answer,
                            "source": "dynamic_mcp_error",
                            "documents": [],
                            "source_urls": [],
                            "score": 0.0,
                            "decision": "dynamic_mcp_failed",
                            "decision_reason": "Dynamic MCP server query failed",
                            "collections_searched": [],
                            "final_context": [],
                            "plan": plan,
                            "reasoning": {"final_reasoning": "Dynamic MCP server failed"},
                            "mode": mode
                        }
            except Exception as e:
                logger.error(f"Error querying dynamic agent: {e}")
                exception_answer = f"Error querying dynamic MCP server: {str(e)}"

                # Store chat history for exception case
                interaction_metadata = {
                    "mode": mode,
                    "host": host,
                    "model": model,
                    "decision": "dynamic_mcp_error",
                    "score": 0.0,
                    "plan_id": plan["plan_id"]
                }

                memory_manager.store_interaction(
                    query=query,
                    response=exception_answer,
                    chat_id=chat_id,
                    user_id=user_id,
                    metadata=interaction_metadata
                )
                logger.info(f"Stored dynamic_mcp exception interaction in chat history for chat_id={chat_id}")

                return {
                    "answer": exception_answer,
                    "source": "dynamic_mcp_error",
                    "documents": [],
                    "source_urls": [],
                    "score": 0.0,
                    "decision": "dynamic_mcp_error",
                    "decision_reason": f"Exception in dynamic MCP query: {str(e)}",
                    "collections_searched": [],
                    "final_context": [],
                    "plan": plan,
                    "reasoning": {"final_reasoning": "Dynamic MCP server exception"},
                    "mode": mode
                }

        # For other modes (rag, agentic, web), proceed with normal flow - NO dynamic agent involvement
        coordination_result = await aggregator_agent.coordinate_query(
            query=query,
            mode=mode,
            collections=collections
        )
        logger.info(f"🔄 Coordination complete: {coordination_result['decision']}")
        
        reasoning = planning_manager.chain_of_thought_reasoning(query, coordination_result)
        logger.info(f"🧠 CoT reasoning: {reasoning['final_reasoning']}")
        
        final_context = coordination_result.get("final_context", [])
        combined_history = f"{memory_context}\n{history}" if memory_context else history
        
        final_answer = await generative_model.generate_response(
            query=query,
            context=final_context,
            host=host,
            model=model,
            api_key=api_key,
            history=combined_history,
            mode=mode,
            stream=False
        )
        raw_answer = final_answer.get("answer", "")
        validated_answer = generative_model.validate_response(raw_answer, mode, final_context)
        # validated_answer = generative_model.validate_response(final_answer, mode, final_context)
        
        interaction_metadata = {
            "mode": mode,
            "host": host,
            "model": model,
            "decision": coordination_result["decision"],
            "score": coordination_result["score"],
            "plan_id": plan["plan_id"]
        }
        
        memory_manager.store_interaction(
            query=query,
            response=validated_answer,
            chat_id=chat_id,
            user_id=user_id,
            metadata=interaction_metadata
        )
        
        return {
            "answer": validated_answer,
            "source": coordination_result.get("source"),
            "documents": coordination_result.get("documents", []),
            "source_urls": coordination_result.get("source_urls", []),
            "score": coordination_result.get("score", 0.0),
            "decision": coordination_result.get("decision", ""),
            "decision_reason": coordination_result.get("decision_reason", ""),
            "collections_searched": coordination_result.get("collections_searched", collections),
            "final_context": coordination_result.get("final_context", []),
            "plan": plan,
            "reasoning": reasoning,
            "mode": mode
        }
    except Exception as e:
        logger.error(f"❌ Error in Multi-Agent processing: {e}")
        return {
            "answer": f"Error in Multi-Agent processing: {str(e)}",
            "source": "Error",
            "documents": [],
            "source_urls": [],
            "score": 0.0,
            "decision": "error",
            "mode": mode
        }

# LangGraph Flow Definition
class GraphState(TypedDict):
    query: str
    collections: List[str]
    host: str
    model: str
    api_key: str
    context: List[str]
    score: float
    web_context: List[str]
    final_context: List[str]
    answer: str
    documents: List[dict]
    mode: str
    chat_id: Optional[str]
    decision: str
    source_urls: List[str]
    history: str
    server_id: Optional[str] = None  # New field for dynamic_mcp mode

#from server.dynamic_agent_manager1 import DynamicAgentManager
#dynamic_agent_manager1 = DynamicAgentManager()


from server.dynamic_agent_manager import get_global_agent_manager


# Helper function to query dynamic MCP server
async def query_dynamic_mcp_node(state: GraphState) -> GraphState:
    logger.info(f"Querying dynamic MCP server for server_id: {state.server_id}")
    try:
        if not state.server_id:
            state.context = "For dynamic_mcp mode, you need to create a valid dynamic MCP server to answer the query."
            state.decision = "mcp_error"
            logger.warning("No server_id provided for dynamic_mcp mode")
            return state
        mcp_response = await query_user_dynamic_agent(state.user_id, state.query, "dynamic_mcp")
        if mcp_response.get("success"):
            state.context = mcp_response.get("result", "")
            state.documents = []  # Dynamic MCP doesn't return documents in this format
            state.source_urls = []
            state.score = 1.0
            state.decision = "use_mcp_results"
            logger.info(f"Dynamic MCP server returned result")
        else:
            error_msg = mcp_response.get("error", "Unknown error")
            state.context = f"Dynamic MCP server error: {error_msg}"
            state.decision = "mcp_error"
            logger.error(f"Dynamic MCP server failed: {error_msg}")
            logger.warning("Dynamic MCP server returned no valid results")
    except Exception as e:
        state.context = "For dynamic_mcp mode, you need to create a valid dynamic MCP server to answer the query."
        state.decision = "mcp_error"
        logger.error(f"Error querying dynamic MCP server: {e}")
    return state

async def qdrant_search_node(state):
    query = state["query"]
    collections_input = state.get("collections", [])
    if not collections_input or not any(c.strip() for c in collections_input if c):
        collections = [QDRANT_DEFAULT_COLLECTION]
        logger.info(f"No valid collections provided, using default: {QDRANT_DEFAULT_COLLECTION}")
    else:
        collections = [c.strip() for c in collections_input if c and c.strip()]
        if not collections:
            collections = [QDRANT_DEFAULT_COLLECTION]
            logger.info(f"All collections were empty, using default: {QDRANT_DEFAULT_COLLECTION}")
    
    logger.info(f"Processing collections: {collections}")
    
    combined_context = []
    best_score = 0.0
    files = []
    all_hits = []
    valid_collections_found = False
    
    try:
        qdrant_tool = await get_qdrant_tool()
        for collection in collections:
            if not await ensure_qdrant_collection(collection):
                logger.debug(f"Collection {collection} does not exist; skipping")
                continue
            valid_collections_found = True
            try:
                results = await qdrant_tool.coroutine(query=query, collection_name=collection)
                logger.info(f"Qdrant MCP tool results for collection {collection}: {results}")
                if isinstance(results, tuple):
                    results = results[0]
                if isinstance(results, str):
                    results = json.loads(results)
                if not isinstance(results, list):
                    logger.error(f"Unexpected Qdrant results format: {type(results)}")
                    continue
                for hit in results:
                    if isinstance(hit, str):
                        try:
                            hit = json.loads(hit.strip())
                        except json.JSONDecodeError as e:
                            logger.error(f"Failed to parse Qdrant hit: {hit}, error: {e}")
                            continue
                    if not isinstance(hit, dict):
                        logger.error(f"Invalid Qdrant hit format: {hit}")
                        continue
                    ctx = hit.get("text", "").strip()
                    filename = hit.get("metadata", {}).get("source", "unknown.pdf")
                    score = float(hit.get("score", 0.0))
                    if ctx:
                        all_hits.append({
                            "context": ctx,
                            "filename": filename,
                            "score": score,
                            "collection": collection
                        })
                        best_score = max(best_score, score)
                        files.append({"file": filename, "collection": collection})
            except Exception as e:
                logger.error(f"Error invoking qdrant_find for collection {collection}: {str(e)}")
    except Exception as e:
        logger.error(f"Error fetching qdrant_find tool: {str(e)}")
        return {**state, "context": [], "score": 0.0, "documents": [], "source_urls": []}
    
    if not valid_collections_found:
        logger.info("No valid Qdrant collections found; skipping Qdrant search")
        return {**state, "context": [], "score": 0.0, "documents": [], "source_urls": []}
    
    all_hits = sorted(all_hits, key=lambda x: x["score"], reverse=True)[:3]
    for hit in all_hits:
        combined_context.append(hit["context"])
    
    logger.info(f"Combined context: {combined_context}")
    logger.info(f"Best Qdrant score: {best_score}")
    logger.info(f"Documents: {files}")
    return {**state, "context": combined_context, "score": best_score, "documents": files, "source_urls": []}

def relevance_check_node(state):
    mode = state.get("mode", "agentic")
    score = state.get("score", 0.0)
    logger.info(f"Relevance check - Mode: {mode}, Score: {score}, Context length: {len(state.get('context', []))}")
    
    if mode == "rag":
        decision = "only_rag"
    elif mode == "web":
        decision = "force_web"
    elif mode == "dynamic_mcp":
        decision = "only_dynamic_mcp"
    elif mode == "agentic":
        decision = "needs_web_search" if score < 0.3 else "good_enough"
        logger.info(f"[Agentic] Score: {score} -> Decision: {decision}")
    else:
        decision = "only_rag"
    return {**state, "decision": decision, "documents": state.get("documents", []), "source_urls": []}

def search_serper_node(state):
    query = state["query"]
    source_urls = []
    web_context = []
    try:
        payload = {"q": query}
        headers = {
            "Content-Type": "application/json",
            "X-API-Key": os.getenv("SERPER_API_KEY")
        }
        response = requests.post(SERPER_MCP_URL, json=payload, headers=headers)
        response.raise_for_status()
        result = response.json().get("organic", [])
        if result and isinstance(result, list):
            for item in result[:3]:
                if item.get('link'):
                    source_urls.append(item['link'])
            for item in result[:3]:
                snippet = f"{item.get('title', '')}: {item.get('snippet', '')} ({item.get('link', '')})"
                web_context.append(snippet)
    except requests.exceptions.RequestException as e:
        logger.error(f"Error with google_search: {str(e)}")
    return {**state, "web_context": web_context, "documents": [], "source_urls": source_urls}

def combine_context_node(state):
    mode = state.get("mode", "agentic")
    score = state.get("score", 0.0)
    qdrant_context = state.get("context", [])
    web_context = state.get("web_context", [])
    dynamic_mcp_context = state.get("dynamic_mcp_context", [])

    if mode == "dynamic_mcp":
        combined = dynamic_mcp_context
    elif mode== "agentic" and score >= 0.3 and qdrant_context:
        combined = qdrant_context
    elif mode == "agentic":
        #if not qdrant_context and web_context:
            #combined = web_context
        #else:
        combined = qdrant_context + web_context
    elif mode == "web":
        combined = web_context
    else:
        combined = qdrant_context
    
    logger.info(f"Combined context length: {len(combined)}")
    return {
        **state,
        "final_context": combined,
        "documents": state.get("documents", []) if mode not in ["web", "dynamic_mcp"] else [],
        "source_urls": state.get("source_urls", [])
    }

def generate_final_answer_node(state):
    host = state["host"]
    model = state["model"]
    api_key = state["api_key"]
    mode = state.get("mode", "agentic")
    context_list = state.get("final_context", state.get("context", []))
    context_str = "\n".join([f"- {c}" for c in context_list]) if context_list else "No relevant information found."
    source = state.get("source", "qdrant")
    source_urls = state.get("source_urls", [])
    history_str = state.get("history", "")
    
    if mode == "dynamic_mcp":
        dynamic_mcp_context = state.get("final_context", state.get("dynamic_mcp_context", []))
        if dynamic_mcp_context:
            answer = "\n".join(dynamic_mcp_context)
        else:
            answer = "No dynamic MCP server results found."
        return {
            **state,
            "answer": answer,
            "documents": [],
            "source_urls": []
        }
    elif mode  == "web":
        web_context = state.get("final_context", state.get("web_context", []))
        if web_context:
            answer = "\n".join(web_context)
        else:
            answer = "No web search results found."
        return {
            **state,
            "answer": answer,
            "documents": [],
            "source_urls": state.get("source_urls", [])
        }
    elif mode == "agentic":
        qdrant_score = state.get("score", 0.0)
        qdrant_context = state.get("final_context", state.get("context", []))
        web_context = state.get("web_context", [])
        if not qdrant_context or qdrant_score < 0.3:
            if web_context:
                answer = "\n".join(web_context)
            else:
                answer = "No web search results found."
            return {
                **state,
                "answer": answer,
                "documents": [],
                "source_urls": state.get("source_urls", [])
            }
    elif mode == "rag" and not context_list:
        logger.warning("No context found in rag mode; returning fallback response")
        return {
            **state,
            "answer": "Sorry, no relevant information was found in the provided documents.",
            "documents": [],
            "source_urls": []
        }
    
    prompt = f"""
You are a helpful AI assistant providing comprehensive and detailed responses.

Response Instructions:

You are an intelligent assistant that answers user queries using the information retrieved from context, chat history, or MCP tools. Provide detailed, informative responses.

- When mode is 'rag', use ONLY the provided Context from Qdrant documents to answer the query.
- In rag mode, if no Context is provided or Context is empty, respond EXACTLY with:
"Sorry, no relevant information was found in the provided documents."
- In rag mode, DO NOT use any general knowledge, outside information, or assumptions.
- When mode is 'agentic', use Context first; if insufficient, use web search results.
- When mode is 'web', use ONLY the provided web search results to answer the query.
- When mode is 'dynamic_mcp', use ONLY the provided Context from dynamic MCP server results to answer the query.
- Base your response on the provided Context and History.
- In rag mode if the decison is for the fallback response then in the document section it will give the empty.

Answering Rules:
- Provide comprehensive, detailed, and informative responses based on the Context/History.
- Give complete explanations with relevant details and examples when available.
- Structure your response clearly with proper formatting when appropriate.
- Never use outside knowledge beyond what's provided in the context.
- Do not include any URLs inside the main answer.
- Ensure the answer is factual, clear, complete, and well-explained.
- Do not mention "Based on the context and chat history" in your response.
- Do not ask questions in your final answer - provide complete information.
- Extract ALL specific information from the context and chat history.
- Provide comprehensive coverage with multiple detailed paragraphs when appropriate.
- Include relevant timelines, data points, and significant information when available.
- Do not include any Response Prompt Instructions in the final answer.
- Use multiple paragraphs covering different aspects of the topic, and in this topics give the information those topics do not give any suggestions or instruction.
- Do not include this type of suggestions and instuction in the answer like "Detailed information can be found on Wikipedia and other websites.", visist this link/page.
- Do not start the answer using this "According to the provided context", directly start the actual answer.

- After providing the main answer, generate ONE relevant follow-up question to continue the conversation.
- The follow-up question must be based on the query and context, suggesting a deeper exploration of the topic or related information.
- Format the follow-up as: \n\n👉 [Your suggested question here, phrased as 'Would you like me to...' or 'Are you interested in...']
- Ensure the follow-up is specific, concise, and directly related to the query/context.
- Do not generate a follow-up if the response is an error message or if no relevant context is provided.
- Do not include sensitive or inappropriate content in the response or follow-up.

You MUST provide detailed, specific, factual responses. DO NOT give generic responses that redirect users to websites.

STRICTLY FORBIDDEN RESPONSES:
- NEVER say "information can be found on Wikipedia/websites/platforms"
- NEVER say "readily available on various platforms"
- NEVER say "Wikipedia page provides" or "LinkedIn profile shows"
- NEVER say "comprehensive overview" or "in-depth look"
- NEVER use generic phrases like "renowned", "prominent figure", "well-known" without specific details
- NEVER give template-like responses that lack concrete facts
- NEVER redirect users to external sources instead of providing information
- NEVER mention checking external websites or platforms
- DO NOT add unnecessary "Introduction" sections - start directly with the main content
- Only use introductory text if it adds essential context, otherwise jump straight to the answer
- For factual queries, provide the facts immediately without preamble
- Do not give any Headings in the fisrt of the answer start with the normal answer.

FORMATTING REQUIREMENTS:
- Structure your response clearly with proper formatting
- Use bullet points (•) for listing key facts or achievements
- Use numbered lists (1., 2., 3.) for sequential information or steps
- Use subheadings with **bold text** when covering different aspects
- Break long content into digestible paragraphs
- Use line breaks between different sections
- Format important dates, numbers, and statistics clearly
- Make the response easy to read and well-organized

- Structure information logically with proper formatting and organization
- Use bullet points (•) and numbered lists (1., 2., 3.) to organize key information
- Add **bold subheadings** when covering different aspects of a topic
- Break content into digestible sections with line breaks between topics
- NEVER start with "Introduction to [topic]" or "**Introduction to [topic]**"
- Provide complete, substantial, fact-dense responses that are well-structured and easy to read querys answers

Context:
{context_str}

History:
{history_str}

Query:
{state['query']}

Your answer:
"""
    
    try:
        headers = {"Content-Type": "application/json"}
        if host == "spaarxsenseaifabric":
            llm_payload = {
                "model": model,
                "prompt": prompt,
                "stream": False
            }
            headers["Authorization"] = f"Bearer {api_key}"
            response = requests.post(SPAARXSENSE_AI_FABRIC_ENDPOINT, json=llm_payload, headers=headers)
            response.raise_for_status()
            data = response.json()
            answer = data.get("response", "No answer found.")
        elif host == "groq":
            llm_payload = {
                "model": model,
                "messages": [{"role": "user", "content": prompt}],
                "stream": False
            }
            headers["Authorization"] = f"Bearer {api_key}"
            response = requests.post(GROQ_ENDPOINT, json=llm_payload, headers=headers)
            response.raise_for_status()
            data = response.json()
            answer = data.get("choices", [{}])[0].get("message", {}).get("content", "No answer found.")
        elif host == "google":
            llm_payload = {
                "contents": [{"parts": [{"text": prompt}]}]
            }
            headers["x-goog-api-key"] = api_key
            response = requests.post(GOOGLE_ENDPOINT, json=llm_payload, headers=headers)
            response.raise_for_status()
            data = response.json()
            answer = data.get("candidates", [{}])[0].get("content", {}).get("parts", [{}])[0].get("text", "No answer found.")
        elif host == "meta":
            llm_payload = {"text": prompt}
            headers["Authorization"] = f"Bearer {api_key}"
            response = requests.post(f"{HUGGINGFACE_ENDPOINT}/{model}", json=llm_payload, headers=headers)
            response.raise_for_status()
            data = response.json()
            answer = data[0].get("generated_text", "No answer found.")
        elif host == "microsoft":
            llm_payload = {"text": prompt}
            headers["Authorization"] = f"Bearer {api_key}"
            response = requests.post(f"{HUGGINGFACE_ENDPOINT}/{model}", json=llm_payload, headers=headers)
            response.raise_for_status()
            data = response.json()
            answer = data[0].get("generated_text", "No answer found.")
        elif host == "anthropic":
            llm_payload = {
                "model": model,
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": MAX_TOKENS
            }
            headers.update({
                "x-api-key": api_key,
                "anthropic-version": "2023-06-01"
            })
            response = requests.post(ANTHROPIC_ENDPOINT, json=llm_payload, headers=headers)
            response.raise_for_status()
            data = response.json()
            answer = data.get("content", [{}])[0].get("text", "No answer found.")
        elif host == "openai":
            llm_payload = {
                "model": model,
                "messages": [{"role": "user", "content": prompt}],
                "stream": False
            }
            headers["Authorization"] = f"Bearer {api_key}"
            response = requests.post(OPENAI_ENDPOINT, json=llm_payload, headers=headers)
            response.raise_for_status()
            data = response.json()
            answer = data.get("choices", [{}])[0].get("message", {}).get("content", "No answer found.")
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported host: {host}")
        
        source_section = ""
        if mode == "web" or (mode == "agentic" and not state.get("documents")):
            source_section = "\n\nSource: web"
            urls = source_urls[:3]
            if urls:
                source_section += "\nSource URLs:"
                for url in urls:
                    source_section += f"\n- {url}"
        elif state.get("documents"):
            source_section = "\n\nSource: qdrant"
            docs = state.get("documents", [])
            if docs:
                source_section += "\nDocuments:"
                for doc in docs:
                    collection = doc.get("collection", "unknown_collection")
                    file = doc.get("file", doc.get("filename", "unknown_file"))
                    source_section += f"\n- Collection: {collection}\n  - File: {file}"
        else:
            source_section = "\n\nSource: unknown"

        
        return {
            **state,
            "answer": answer + source_section,
            "source": "web" if (mode == "web" or (mode == "agentic" and not state.get("documents"))) else "qdrant",
            "documents": state.get("documents", []) if mode != "web" else [],
            "source_urls": source_urls[:3] if (mode == "web" or (mode == "agentic" and not state.get("documents"))) else []
        }
    except requests.exceptions.RequestException as req_err:
        logger.error(f"LLM API request failed for host {host}: {str(req_err)}")
        answer = "Error connecting to the LLM service."
        return {
            **state,
            "answer": answer,
            "documents": [],
            "source_urls": []
        }

graph = StateGraph(GraphState)
graph.add_node("start", lambda state: state)
graph.add_node("qdrant_search", qdrant_search_node)
graph.add_node("relevance_check", relevance_check_node)
graph.add_node("search_serper", search_serper_node)
graph.add_node("dynamic_mcp_search", query_dynamic_mcp_node)
graph.add_node("combine_context", combine_context_node)
graph.add_node("generate_final_answer", generate_final_answer_node)

graph.add_edge("start", "qdrant_search")
graph.add_edge("qdrant_search", "relevance_check")
graph.add_conditional_edges(
    "relevance_check",
    lambda state: state["decision"],
    {
        "good_enough": "generate_final_answer",
        "needs_web_search": "search_serper",
        "only_rag": "generate_final_answer",
        "force_web": "search_serper",
    }
)
graph.add_edge("search_serper", "combine_context")
graph.add_edge("combine_context", "generate_final_answer")
graph.add_edge("dynamic_mcp_search", "generate_final_answer")
#graph.set_entry_point("start")
graph.set_conditional_entry_point(
        lambda state: "dynamic_mcp_search" if state.mode == "dynamic_mcp" else "qdrant_search",
        {
            "dynamic_mcp_search": "dynamic_mcp_search",
            "qdrant_search": "qdrant_search"
        }
    )
agent_graph = graph.compile()

# FastAPI Endpoints
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app):
    logger.info("🚀 Starting FastAPI application...")
    print("\n" + "="*60)
    print("🚀 FASTAPI STARTUP - TESTING CONNECTIONS")
    print("="*60)

    # Initialize MCP client first
    mcp_init_status = initialize_mcp_client()

    redis_status = test_redis_connection()
    qdrant_status = await test_qdrant_mcp_connection() if mcp_init_status else False
    serper_status = await test_serper_connection()
    
    print("\n" + "="*60)
    print("📊 FINAL CONNECTION STATUS SUMMARY")
    print("="*60)
    print(f"Redis:   {'✅ CONNECTED' if redis_status else '❌ DISCONNECTED'}")
    print(f"Qdrant:  {'✅ CONNECTED' if qdrant_status else '❌ DISCONNECTED'}")
    print(f"Serper:  {'✅ CONNECTED' if serper_status else '❌ DISCONNECTED'}")
    print("="*60)
    
    if redis_status and qdrant_status and serper_status:
        print("🎉 ALL SERVICES CONNECTED!")
        logger.info("🎉 FastAPI startup successful - All services connected!")
    elif redis_status or qdrant_status or serper_status:
        print("⚠️ PARTIAL CONNECTIVITY")
        logger.warning("⚠️ FastAPI startup with partial connectivity")
    else:
        print("⚠️ LIMITED CONNECTIVITY")
        logger.warning("⚠️ FastAPI startup with limited connectivity")
    
    yield
    
    logger.info("🔄 Shutting down FastAPI application...")
    try:
        if redis_client:
            redis_client.close()
        logger.info("✅ AWS Redis connection closed")
    except Exception as e:
        logger.warning(f"⚠️ Error closing AWS Redis connection: {str(e)}")
    logger.info("👋 FastAPI application shutdown complete")

app = FastAPI(title="MCP Retrieval-SpaarxSense", lifespan=lifespan)

# Custom schema modifier to fix additionalProp1 issue
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    from fastapi.openapi.utils import get_openapi
    openapi_schema = get_openapi(
        title="MCP Retrieval-SpaarxSense",
        version="1.0.0",
        description="Dynamic MCP Server Management API",
        routes=app.routes,
    )

    # Debug: Print schema structure
    print("🔍 Debugging OpenAPI Schema Generation...")

    # Fix the credentials field schema to remove additionalProp1
    if "components" in openapi_schema and "schemas" in openapi_schema["components"]:
        schemas = openapi_schema["components"]["schemas"]

        if "CreateMCPServerRequest" in schemas:
            schema = schemas["CreateMCPServerRequest"]
            print(f"✅ Found CreateMCPServerRequest schema")

            if "properties" in schema and "credentials" in schema["properties"]:
                print(f"✅ Found credentials property")
                print(f"🔍 Original credentials schema: {schema['properties']['credentials']}")

                # Replace the generic additionalProp1 with proper examples
                schema["properties"]["credentials"] = {
                    "type": "object",
                    "title": "Credentials",
                    "description": "Credentials for your MCP server. Provide the key-value pairs needed for your specific service.",
                    "example": {"api_key": "your_api_key_here"},
                    "additionalProperties": True,
                    "default": {}
                }

                print(f"✅ Updated credentials schema: {schema['properties']['credentials']}")
            else:
                print("❌ Credentials property not found in schema")
        else:
            print("❌ CreateMCPServerRequest schema not found")
    else:
        print("❌ Components or schemas not found in OpenAPI schema")

    app.openapi_schema = openapi_schema
    return app.openapi_schema

# Set the custom OpenAPI schema
app.openapi = custom_openapi

@app.post("/new_chat", response_model=NewChatResponse)
async def new_chat(request: Request):
    try:
        user_id = get_user_id(request)
        chat_id = create_new_chat(user_id)
        client_ip = request.client.host
        active_sessions[client_ip]["current_chat_id"] = chat_id
        active_sessions[client_ip]["last_accessed_history"] = chat_id
        logger.info(f"New chat created: chat_id={chat_id}, user_id={user_id}")
        return NewChatResponse(chat_id=chat_id)
    except Exception as e:
        logger.error(f"Error creating new chat: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating new chat: {str(e)}")

@app.get("/history/{chat_id}", response_model=HistoryResponse)
async def get_history(chat_id: str, request: Request):
    try:
        full_doc = get_session_history(chat_id)
        if not full_doc or not any(v for k, v in full_doc.items() if k != 'messages'):
            raise HTTPException(status_code=404, detail=f"Chat history for ID {chat_id} is empty or invalid.")
        client_ip = request.client.host
        user_id = get_user_id(request)
        if client_ip not in active_sessions:
            active_sessions[client_ip] = {
                "user_id": user_id,
                "current_chat_id": None,
                "last_accessed_history": None
            }
        active_sessions[client_ip]["current_chat_id"] = chat_id
        active_sessions[client_ip]["last_accessed_history"] = chat_id
        session_metadata = get_session_metadata(user_id, chat_id)
        if not session_metadata:
            messages = full_doc.get('messages', [])
            if messages:
                last_assistant_msg = None
                for msg in reversed(messages):
                    if msg.get('role') == 'assistant':
                        last_assistant_msg = msg
                        break
                if last_assistant_msg and last_assistant_msg.get('metadata'):
                    metadata = last_assistant_msg['metadata']
                    restored_metadata = {
                        'mode': metadata.get('mode', 'agentic'),
                        'host': metadata.get('host', ''),
                        'model': metadata.get('model', ''),
                        'restored_from_mongodb': 'true',
                        'last_query_time': datetime.now()
                    }
                    store_session_metadata(user_id, chat_id, restored_metadata)
                    logger.info(f"Restored session metadata from MongoDB for chat_id={chat_id}")
        logger.info(f"Retrieved history for chat_id={chat_id}")
        return JSONResponse(content=full_doc)
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error fetching history for chat_id={chat_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching history: {str(e)}")

@app.post("/create_mcp_server", response_model=CreateMCPServerResponse)
async def create_mcp_server_endpoint(http_request: Request, request: CreateMCPServerRequest):
    logger.info(f"Received MCP server create request: {request.dict()}")
    try:
        # Define validation dictionaries
        valid_categories = {
            "custom": ["cloud_storage", "databases", "devops", "git"],
            "public": ["locally_available"]
        }
        valid_services = {
            "cloud_storage": ["aws_s3", "gcs_drive", "google_drive", "microsoft_sharepoint", "azure_blob", "onedrive"],
            "databases": ["postgres", "mysql", "mongodb", "redis", "sql_server", "sqlite"],
            "devops": ["jira", "azure_devops", "github_actions", "gitlab_ci", "jenkins"],
            "git": ["github", "gitlab", "bitbucket", "azure_repos"],
            "locally_available": ["airbnb", "news", "google", "weather", "brave_search", "google_maps"]
        }

        # Validate inputs based on server type
        if request.server_type not in valid_categories:
            raise HTTPException(status_code=400, detail=f"Invalid server type: {request.server_type}")

        if request.server_type == "custom":
            if not request.category:
                raise HTTPException(status_code=400, detail="Category is required for custom servers")
            if not request.service:
                raise HTTPException(status_code=400, detail="Service is required for custom servers")
            if not request.credentials:
                raise HTTPException(status_code=400, detail="Credentials are required for custom servers")
            if request.category not in valid_categories["custom"]:
                raise HTTPException(status_code=400, detail=f"Invalid category '{request.category}' for custom server")
            if request.service not in valid_services.get(request.category, []):
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid service '{request.service}' for custom server category '{request.category}'"
                )
        elif request.server_type == "public":
            if not request.service:
                raise HTTPException(status_code=400, detail="Service is required for public servers")
            if request.service not in valid_services["locally_available"]:
                raise HTTPException(status_code=400, detail=f"Invalid service '{request.service}' for public server")
            request.credentials = request.credentials or {}
            request.category = "locally_available"  # Set for dynamic_mcp_factory compatibility

        # Generate user_id if not provided
        if request.user_id and request.user_id != "string":
            user_id = request.user_id
        else:
            user_id = get_user_id(http_request)

        # Create dynamic MCP server with stdio transport
        response = await create_dynamic_mcp_server(
            server_type=request.server_type,
            category=request.category,
            service=request.service,
            credentials=request.credentials,
            server_name=request.server_name,
            description=request.description or "",
            user_id=user_id,
            #transport="stdio"
        )
        server_id = response["server_id"]
        user_id = response["user_id"]
        script_path = response["script_path"]  # Get script path

        # Start the server
        started = await start_dynamic_mcp_server(server_id)
        if not started:
            raise HTTPException(status_code=500, detail=f"Failed to start MCP server {server_id}")
        
        # Verify server status
        server_info = get_dynamic_mcp_server_info(server_id)
        if not server_info or server_info["status"] != "running":
            await delete_dynamic_mcp_server(server_id)  # Rollback
            raise HTTPException(status_code=500, detail=f"MCP server {server_id} is not running")

        # Wait a bit more for the server to be fully ready
        await asyncio.sleep(3)
        #script_path= "server\dynamic_mcp_factory1.py"
        #script_path = "D:\Generative_AI\mcpcourse\final_deployed_spaarxsense_files\today\dynamic_servers"
        # Create dynamic agent
        agent_name = f"{request.service}_agent_{server_id[:8]}"
        try:
            agent_id = await create_dynamic_agent(user_id, server_id, agent_name, script_path)
        except Exception as e:
            logger.error(f"Agent creation failed for server {server_id}: {str(e)}")
            await delete_dynamic_mcp_server(server_id)  # Rollback
            raise HTTPException(status_code=500, detail=f"Error creating agent: {str(e)}")
        
        # Get server info
        server_info_dict = get_dynamic_mcp_server_info(server_id)
        if not server_info_dict:
            await delete_dynamic_mcp_server(server_id)  # Rollback
            raise HTTPException(status_code=500, detail="Failed to retrieve server info")

        # Construct server info response
        server_info = MCPServerInfo(
            server_id=server_info_dict["server_id"],
            server_name=server_info_dict["server_name"],
            server_type=request.server_type,
            category=request.category,
            service=request.service,
            status=server_info_dict["status"],
            createdAt=server_info_dict["createdAt"],
            last_used=server_info_dict.get("last_used"),
            user_id=user_id
        )

        logger.info(f"Created and started dynamic MCP server: {server_id}")
        return CreateMCPServerResponse(
            success=True,
            server_id=server_id,
            server_info=server_info,
            message=f"Successfully created and started {request.server_type} MCP server for {request.service} with dynamic agent"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating MCP server: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating MCP server: {str(e)}")

@app.get("/mcp_server_status")
async def get_mcp_server_status(request: Request):
    try:
        user_id = get_user_id(request)
        servers = []
        for server_data in dynamic_mcp_factory.servers.values():
            server_info = MCPServerInfo(
                server_id=server_data["server_id"],
                server_name=server_data["server_name"],
                server_type=MCPServerType(server_data.get("server_type", "custom")),
                category=server_data.get("category"),
                service=server_data.get("service"),
                status=server_data["status"],
                createdAt=server_data["createdAt"],
                last_used=server_data.get("last_used"),
                user_id=server_data.get("user_id")
            )
            servers.append(server_info)
        
        factory_status = {
            "factory_initialized": dynamic_mcp_factory is not None,
            "total_servers": len(servers),
            "running_servers": len([s for s in servers if s.status == "running"]),
            "total_count": len(servers),
            "user_id": user_id,
            "available_services": {
                "cloud_storage": [e.value for e in CloudStorageService],
                "databases": [e.value for e in DatabaseService],
                "devops_tools": [e.value for e in DevOpsService],
                "git": [e.value for e in GitService],
                "public": [e.value for e in PublicService]
            }
        }
        
        return {
            "status": "operational",
            "factory_status": factory_status,
            "servers": servers,
            "timestamp": datetime.now()
        }
    except Exception as e:
        logger.error(f"Error getting MCP server status: {e}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now()
        }

@app.get("/all_agents")
async def get_all_agents_endpoint():
    try:
        agents = get_all_dynamic_agents()
        return {
            "agents": agents,
            "total_count": len(agents)
        }
    except Exception as e:
        logger.error(f"Error getting all agents: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting all agents: {str(e)}")

@app.delete("/delete_mcp_server/{server_id}", response_model=DeleteMCPServerResponse)
async def delete_mcp_server(server_id: str):
    try:
        server_info = get_dynamic_mcp_server_info(server_id)
        if not server_info:
            raise HTTPException(status_code=404, detail=f"MCP server {server_id} not found")
        
        agents = get_all_dynamic_agents()
        for agent in agents:
            if agent.get('server_id') == server_id:
                deleted = await delete_dynamic_agent(agent['agent_id'])
                if deleted:
                    logger.info(f"Deleted dynamic agent {agent['agent_id']} for server {server_id}")
                else:
                    logger.warning(f"Failed to delete dynamic agent {agent['agent_id']} for server {server_id}")
        
        deleted = await delete_dynamic_mcp_server(server_id)
        if not deleted:
            raise HTTPException(status_code=500, detail="Failed to delete MCP server")
        
        logger.info(f"Deleted dynamic MCP server: {server_id}")
        return DeleteMCPServerResponse(
            success=True,
            message=f"Successfully deleted MCP server {server_id} and associated agent"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting MCP server {server_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error deleting MCP server: {str(e)}")


# @app.post("/query")
def clean_documents(docs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Keep only allowed keys from each document in the response."""
    allowed_keys = {"file", "collection"}  # you can add more if needed
    return [{k: v for k, v in doc.items() if k in allowed_keys} for doc in docs]


@app.post("/query", response_model=QueryResponse)
async def query(request: Request, query_request: QueryRequest):
    chat_id = None
    try:
        user_id_obj = memory_manager._validate_object_id(query_request.user_id, "user_id")
        user_id = str(user_id_obj)
        if not user_id:
            raise HTTPException(status_code=400, detail="user_id is required.")

        chat_id = query_request.chat_id
        if chat_id:
            try:
                chat_id_obj = memory_manager._validate_object_id(chat_id, "chat_id")
                logger.info(f"Using provided valid chat_id: {chat_id}")
            except ValueError:
                chat_id_obj = ObjectId(create_new_chat(user_id))
                chat_id = str(chat_id_obj)
                logger.info(f"Invalid chat_id provided, created new: {chat_id}")
        else:
            chat_id_obj = ObjectId(create_new_chat(user_id))
            chat_id = str(chat_id_obj)
            logger.info(f"No chat_id provided, created new: {chat_id}")

        session_metadata = get_session_metadata(user_id_obj, chat_id_obj)
        mode = query_request.mode or session_metadata.get('mode') or 'agentic'
        host = query_request.host or session_metadata.get('host')
        api_key = query_request.api_key or session_metadata.get('api_key')
        model = query_request.model or session_metadata.get('model')

        if not host:
            raise HTTPException(status_code=400, detail="Host is required for new sessions or when not provided in follow-up questions.")
        if not api_key:
            raise HTTPException(status_code=400, detail="API key is required for new sessions or when not provided in follow-up questions.")
        if not model:
            raise HTTPException(status_code=400, detail="Model is required for new sessions or when not provided in follow-up questions.")
        if host.lower() not in [h.lower() for h in SUPPORTED_HOSTS]:
            raise HTTPException(status_code=400, detail=f"Invalid host: {host}. Choose from: {', '.join(SUPPORTED_HOSTS)}")
        if not validate_model(host.lower(), model, api_key):
            raise HTTPException(status_code=400, detail=f"Model '{model}' is not supported by host '{host}'.")
        valid_modes = {"agentic", "web", "rag", "dynamic_mcp"}
        if mode.lower() not in valid_modes:
            message = f"⚠️ Mode '{mode}' is not supported. Choose from: {', '.join(valid_modes)}."
            return QueryResponse(answer=message, source="system", documents=[], source_urls=[])

        current_metadata = {
            'mode': mode.lower(),
            'host': host.lower(),
            'api_key': api_key,
            'model': model,
            'last_query_time': datetime.now()
        }
        store_session_metadata(user_id_obj, chat_id_obj, current_metadata)

        greeting_response = handle_greeting(query_request.query.strip())
        if greeting_response:
            return QueryResponse(answer=greeting_response, source="greeting", documents=[], source_urls=[])

        is_safe, guardrail_result = run_guardrails(query_request.query, openai_api_key=api_key)
        if not is_safe:
            return QueryResponse(answer=guardrail_result, source="guardrail", documents=[], source_urls=[])

        # 🔄 Check for follow-up question continuation
        original_query = query_request.query
        is_continuation, follow_up_question = handle_follow_up_continuation(
            query_request.query, str(user_id_obj), str(chat_id_obj)
        )

        if is_continuation and follow_up_question:
            logger.info(f"🔄 Follow-up continuation: '{original_query}' -> '{follow_up_question}'")
            query_request.query = follow_up_question  # Replace query with follow-up question

        # First try short-term Redis context
        recent_messages = memory_manager.get_short_term_context(user_id_obj, chat_id_obj, limit=10)

        # If Redis is empty (session expired), restore from MongoDB
        if not recent_messages:
            mongo_messages = memory_manager.get_long_term_context(chat_id_obj, user_id_obj, limit=10)
            for msg in mongo_messages:
                memory_manager.store_short_term_message(user_id_obj, chat_id_obj, msg)
            recent_messages = mongo_messages
            logger.info(f"Restored {len(mongo_messages)} messages to Redis for chat_id={chat_id_obj}")

        history_str = "\n".join([f"{msg['role'].capitalize()}: {msg['content']}" for msg in recent_messages])
        logger.info(f"🧠 Chat history retrieved: {len(history_str)} characters")

        logger.info(f"🤖 Processing with Multi-Agent: mode={mode}, host={host}, model={model}, user={user_id_obj}")

        try:
            multi_agent_result = await process_multi_agent_query(
                query=query_request.query,
                mode=mode,
                collections=query_request.collections or [QDRANT_DEFAULT_COLLECTION],
                host=host,
                model=model,
                api_key=api_key,
                chat_id=chat_id_obj,
                user_id=user_id_obj,
                history=history_str
            )

            result = {
                "query": query_request.query,
                "final_context": multi_agent_result.get("final_context", []),
                "documents": multi_agent_result.get("documents", []),
                "source_urls": multi_agent_result.get("source_urls", []),
                "source": multi_agent_result.get("source", "multi-agent"),
                "decision": multi_agent_result.get("decision", "multi-agent"),
                "score": multi_agent_result.get("score", 0.0)
            }
            result["documents"] = clean_documents(result.get("documents", []))
        except Exception as e:
            logger.error(f"Multi-Agent failed, falling back to legacy graph: {e}")
            state = {
                "query": query_request.query,
                "collections": query_request.collections or [QDRANT_DEFAULT_COLLECTION],
                "host": host,
                "model": model,
                "api_key": api_key,
                "mode": mode,
                "chat_id": chat_id_obj,
                "user_id": user_id_obj,
                "history": history_str,
                "context": [],
                "score": 0.0,
                "web_context": [],
                "final_context": [],
                "answer": "",
                "documents": [],
                "source_urls": [],
                "decision": "",
                "server_id": None
            }
            result = await agent_graph.ainvoke(state)

        documents = result.get("documents", [])
        source_urls = result.get("source_urls", [])[:3]
        base_source = result.get("source", "Multi-Agent")
        if base_source == "qdrant":
            source = "qdrant"
        else:
            source = base_source

        sources = []
        if documents:
            sources.extend([doc.get('file', 'unknown') for doc in documents])

        final_context = result.get("final_context", [])
        feedback_context = get_feedback_context_for_improvement(user_id_obj, chat_id_obj)
        memory_context = memory_manager.get_context(chat_id_obj, user_id_obj)
        context_parts = []
        if memory_context:
            context_parts.append(memory_context)
        if feedback_context:
            context_parts.append(feedback_context)
        if history_str:
            context_parts.append(history_str)
        combined_history = "\n".join(context_parts) if context_parts else ""
        logger.info(f"🧠 Combined history retrieved: {len(combined_history)} characters")
        if combined_history:
            logger.info(f"✅ Combined chat history used as context for query: '{query_request.query}' with chat_id: {chat_id_obj}")
        else:
            logger.info(f"⚠️ No combined chat history available for query: '{query_request.query}' with chat_id: {chat_id_obj}")

        response_dict = await generative_model.generate_response(
            query=query_request.query,
            context=final_context,
            host=host.lower(),
            model=model,
            api_key=api_key,
            history=combined_history,
            mode=mode.lower()
        )
        #full_response = response_dict["answer"]
        #follow_up = response_dict.get["follow_up"]
        # ✅ Strip any model-generated follow-up (we only keep Redis one)
        raw_answer = response_dict.get("answer", "")
        # remove any trailing "👉 ..." part if the model added it
        if "👉" in raw_answer:
            answer = raw_answer.split("👉")[0].strip()
        else:
            answer = raw_answer

        # ✅ Always fetch follow-up from Redis
        follow_up = None
        short_term_msgs = memory_manager.get_short_term_context(user_id, chat_id, limit=20)
        for msg in reversed(short_term_msgs):
            if msg.get("role") == "assistant":
                follow_up = msg.get("metadata", {}).get("follow_up")
                if follow_up:
                    break

        # ✅ Append only Redis follow-up
        if follow_up:
            answer = f"{answer}\n\n👉 {follow_up}"


        cleaned_answer = clean_llm_output(answer)
        
        #cleaned_answer = clean_llm_output(full_response)
        is_safe_out, output_guardrail_result = output_guardrails(cleaned_answer)
        display_response = output_guardrail_result if "safety" not in output_guardrail_result.lower() else cleaned_answer

        is_fallback_response = result.get("is_fallback_response", False)
        if not is_fallback_response:
            is_fallback_response = (
                "no relevant information was found" in cleaned_answer.lower() or
                "Sorry, no relevant information was found in the provided documents." in cleaned_answer.lower()
            )

        if chat_id_obj:
            append_to_chat_history(
                chat_id_obj,
                query_request.query,
                display_response,
                user_id_obj,
                sources=sources,
                source_urls=source_urls,
                mode=mode.lower(),
                host=host.lower(),
                model=model,
                skip_redis=True
            )
            conversation_data = {
                "query": query_request.query,
                "answer": display_response,
                "source": source,
                "source_urls": source_urls,
                "documents": documents,
                "mode": mode.lower(),
                "chat_id": chat_id_obj,
                "server_id": None,
                "timestamp": datetime.now(),
                "follow_up": follow_up
            }
            memory_manager.store_session_metadata(user_id_obj, chat_id_obj, conversation_data)

        logger.info(f"🎉 Multi-Agent query processed: query='{query_request.query}', chat_id={chat_id_obj}, source={source}, mode={mode}")

        return QueryResponse(
            answer=display_response,
            source=source,
            documents=documents,
            source_urls=source_urls,
            chat_id=chat_id
        )

    except requests.exceptions.RequestException as e:
        logger.error(f"Search API failed: {str(e)}")
        return QueryResponse(
            answer="Sorry, no relevant information was found due to server connection issues.",
            source="None",
            documents=[],
            source_urls=[],
            chat_id=chat_id
        )
    except Exception as e:
        logger.error(f"Error processing query: {str(e)}")
        return QueryResponse(
            answer=f"Error processing query: {str(e)}",
            source="None",
            documents=[],
            source_urls=[],
            chat_id=chat_id
        )
@app.post("/rag_query", response_model=QueryResponse)
async def rag_query(request: Request, rag_query_request: RagQueryRequest):
    chat_id = None
    try:
        user_id_obj = memory_manager._validate_object_id(rag_query_request.user_id, "user_id")
        user_id = str(user_id_obj)
        if not user_id:
            raise HTTPException(status_code=400, detail="user_id is required.")

        chat_id = rag_query_request.chat_id
        if chat_id:
            try:
                chat_id_obj = memory_manager._validate_object_id(chat_id, "chat_id")
                logger.info(f"Using provided valid chat_id: {chat_id}")
            except ValueError:
                chat_id_obj = ObjectId(create_new_chat(user_id))
                chat_id = str(chat_id_obj)
                logger.info(f"Invalid chat_id provided, created new: {chat_id}")
        else:
            chat_id_obj = ObjectId(create_new_chat(user_id))
            chat_id = str(chat_id_obj)
            logger.info(f"No chat_id provided, created new: {chat_id}")

        session_metadata = get_session_metadata(user_id_obj, chat_id_obj)
        mode = rag_query_request.mode or session_metadata.get('mode') or 'rag'
        host = rag_query_request.host or session_metadata.get('host')
        api_key = rag_query_request.api_key or session_metadata.get('api_key')
        model = rag_query_request.model or session_metadata.get('model')
        

        if not host:
            raise HTTPException(status_code=400, detail="Host is required for new sessions or when not provided in follow-up questions.")
        if not api_key:
            raise HTTPException(status_code=400, detail="API key is required for new sessions or when not provided in follow-up questions.")
        if not model:
            raise HTTPException(status_code=400, detail="Model is required for new sessions or when not provided in follow-up questions.")
        if host.lower() not in [h.lower() for h in SUPPORTED_HOSTS]:
            raise HTTPException(status_code=400, detail=f"Invalid host: {host}. Choose from: {', '.join(SUPPORTED_HOSTS)}")
        if not validate_model(host.lower(), model, api_key):
            raise HTTPException(status_code=400, detail=f"Model '{model}' is not supported by host '{host}'.")
        valid_modes = {"rag"}
        if mode.lower() not in valid_modes:
            message = f"⚠️ Mode '{mode}' is not supported. Only 'rag' mode is supported."
            return QueryResponse(answer=message, source="system", documents=[])

        current_metadata = {
            'mode': mode.lower(),
            'host': host.lower(),
            'api_key': api_key,
            'model': model,
            'last_query_time': datetime.now()
        }
        store_session_metadata(user_id_obj, chat_id_obj, current_metadata)
        
        greeting_response = handle_greeting(rag_query_request.query.strip())
        if greeting_response:
            return QueryResponse(answer=greeting_response, source="greeting", documents=[])
        
        is_safe, guardrail_result = run_guardrails(rag_query_request.query, openai_api_key=api_key)
        if not is_safe:
            return QueryResponse(answer=guardrail_result, source="guardrail", documents=[])

        # 🔄 Check for follow-up question continuation
        original_query = rag_query_request.query
        is_continuation, follow_up_question = handle_follow_up_continuation(
            rag_query_request.query, str(user_id_obj), str(chat_id_obj)
        )

        if is_continuation and follow_up_question:
            logger.info(f"🔄 Follow-up continuation: '{original_query}' -> '{follow_up_question}'")
            rag_query_request.query = follow_up_question  # Replace query with follow-up question

        # First try short-term Redis context
        recent_messages = memory_manager.get_short_term_context(user_id_obj, chat_id_obj, limit=10)

        # If Redis is empty (session expired), restore from MongoDB
        if not recent_messages:
            mongo_messages = memory_manager.get_long_term_context(chat_id_obj, user_id_obj, limit=10)
            for msg in mongo_messages:
                memory_manager.store_short_term_message(user_id_obj, chat_id_obj, msg)
            recent_messages = mongo_messages
            logger.info(f"Restored {len(mongo_messages)} messages to Redis for chat_id={chat_id_obj}")

        history_str = "\n".join([f"{msg['role'].capitalize()}: {msg['content']}" for msg in recent_messages])
        logger.info(f"🧠 Chat history retrieved: {len(history_str)} characters")
        
        logger.info(f"🤖 Processing with Multi-Agent RAG: mode={mode}, host={host}, model={model}, user={user_id_obj}")
        
        try:
            multi_agent_result = await process_multi_agent_query(
                query=rag_query_request.query,
                mode=mode,
                collections=rag_query_request.collections or [QDRANT_DEFAULT_COLLECTION],
                host=host,
                model=model,
                api_key=api_key,
                chat_id=chat_id_obj,
                user_id=user_id_obj,
                history=history_str
            )
            
            result = {
                "query": rag_query_request.query,
                "final_context": multi_agent_result.get("final_context", []),
                "documents": multi_agent_result.get("documents", []),
                "source": multi_agent_result.get("source", "multi-agent"),
                "decision": multi_agent_result.get("decision", "multi-agent"),
                "score": multi_agent_result.get("score", 0.0)
            }
            result["documents"] = clean_documents(result.get("documents", []))
        except Exception as e:
            logger.error(f"Multi-Agent RAG failed, falling back to legacy graph: {e}")
            state = {
                "query": rag_query_request.query,
                "collections": rag_query_request.collections or [QDRANT_DEFAULT_COLLECTION],
                "host": host,
                "model": model,
                "api_key": api_key,
                "mode": mode,
                "chat_id": chat_id_obj,
                "user_id": user_id_obj,
                "history": history_str,
                "context": [],
                "score": 0.0,
                "final_context": [],
                "answer": "",
                "documents": [],
                "decision": "",
                "server_id": None
            }
            result = await agent_graph.ainvoke(state)
        
        documents = result.get("documents", [])
        base_source = result.get("source", "Multi-Agent RAG")
        
        if base_source == "qdrant":
            source = "qdrant"
        else:
            source = base_source

        sources = []
        if documents:
            sources.extend([doc.get('file', 'unknown') for doc in documents])
        
        final_context = result.get("final_context", [])
        feedback_context = get_feedback_context_for_improvement(user_id_obj, chat_id_obj)
        memory_context = memory_manager.get_context(chat_id_obj, user_id_obj)
        context_parts = []
        if memory_context:
            context_parts.append(memory_context)
        if feedback_context:
            context_parts.append(feedback_context)
        if history_str:
            context_parts.append(history_str)
        combined_history = "\n".join(context_parts) if context_parts else ""
        logger.info(f"🧠 Combined history retrieved: {len(combined_history)} characters")
        if combined_history:
            logger.info(f"✅ Combined chat history used as context for query: '{rag_query_request.query}' with chat_id: {chat_id_obj}")
        else:
            logger.info(f"⚠️ No combined chat history available for query: '{rag_query_request.query}' with chat_id: {chat_id_obj}")

        response_dict = await generative_model.generate_response(
            query=rag_query_request.query,
            context=final_context,
            host=host.lower(),
            model=model,
            api_key=api_key,
            history=combined_history,
            mode=mode.lower()
        )
        #full_response = response_dict["answer"]
        #follow_up = response_dict["follow_up"]

        # ✅ Strip any model-generated follow-up (we only keep Redis one)
        raw_answer = response_dict.get("answer", "")
        # remove any trailing "👉 ..." part if the model added it
        if "👉" in raw_answer:
            answer = raw_answer.split("👉")[0].strip()
        else:
            answer = raw_answer

        # ✅ Always fetch follow-up from Redis
        follow_up = None
        short_term_msgs = memory_manager.get_short_term_context(user_id, chat_id, limit=20)
        for msg in reversed(short_term_msgs):
            if msg.get("role") == "assistant":
                follow_up = msg.get("metadata", {}).get("follow_up")
                if follow_up:
                    break

        # ✅ Append only Redis follow-up
        if follow_up:
            answer = f"{answer}\n\n👉 {follow_up}"


        cleaned_answer = clean_llm_output(answer)
        
        #cleaned_answer = clean_llm_output(full_response)
        is_safe_out, output_guardrail_result = output_guardrails(cleaned_answer)
        display_response = output_guardrail_result if "safety" not in output_guardrail_result.lower() else cleaned_answer
        
        is_fallback_response = result.get("is_fallback_response", False)
        if not is_fallback_response:
            is_fallback_response = (
                "no relevant information was found" in cleaned_answer.lower() or
                "Sorry, no relevant information was found in the provided documents." in cleaned_answer.lower()
            )

        if chat_id_obj:
            append_to_chat_history(
                chat_id_obj,
                rag_query_request.query,
                display_response,
                user_id_obj,
                sources=sources,
                mode=mode.lower(),
                host=host.lower(),
                model=model,
                skip_redis=True
            )
            conversation_data = {
                "query": rag_query_request.query,
                "answer": display_response,
                "source": source,
                "documents": documents,
                "mode": mode.lower(),
                "chat_id": chat_id_obj,
                "server_id": None,
                "timestamp": datetime.now(),
                "follow_up": follow_up
            }
            memory_manager.store_session_metadata(user_id_obj, chat_id_obj, conversation_data)
                
        logger.info(f"🎉 Multi-Agent query processed: query='{rag_query_request.query}', chat_id={chat_id_obj}, source={source}, mode={mode}")

        return QueryResponse(
            answer=display_response,
            source=source,
            documents=documents,
            chat_id=chat_id
        )
    
    except requests.exceptions.RequestException as e:
        logger.error(f"Search API failed: {str(e)}")
        return QueryResponse(
            answer="Sorry, no relevant information was found due to server connection issues.",
            source="None",
            documents=[],
            chat_id=chat_id
        )
    except Exception as e:
        logger.error(f"Error processing query: {str(e)}")
        return QueryResponse(
            answer=f"Error processing query: {str(e)}",
            source="None",
            documents=[],
            chat_id=chat_id
        )

@app.post("/feedback", response_model=FeedbackResponse)
async def submit_feedback(feedback: FeedbackRequest, request: Request):
    """
    Store user feedback for the most recent assistant response in Redis, associated with session and user.
    """
    try:
        #chat_id_obj = memory_manager._validate_object_id(chat_id, "chat_id")
        user_id_obj = get_user_id(request)
        client_ip = request.client.host
        # Get current chat_id from active_sessions using client_ip
        chat_id_obj = None
        if client_ip in active_sessions:
            chat_id_obj = active_sessions[client_ip].get("current_chat_id")
        if not chat_id_obj:
            raise HTTPException(status_code=400, detail="No active chat session found for feedback.")


        # Retrieve recent messages to find the latest assistant response
        session_messages = get_session_messages(user_id_obj, chat_id_obj, limit=10)
        logger.info(f"Retrieved {len(session_messages)} messages for feedback in session {chat_id_obj}")

        if not session_messages:
            # Try to get messages from MongoDB as fallback
            logger.warning(f"No messages found in Redis for session {chat_id_obj}, trying MongoDB fallback")
            mongodb_messages = get_mongodb_history_for_session(chat_id_obj, limit=10)
            if mongodb_messages:
                logger.info(f"Found {len(mongodb_messages)} messages in MongoDB for session {chat_id_obj}")
                session_messages = mongodb_messages
            else:
                logger.error(f"No messages found in either Redis or MongoDB for session {chat_id_obj}")
                raise HTTPException(status_code=404, detail="No messages found in session for feedback.")

        # Find the most recent assistant message
        latest_assistant_msg = None
        assistant_msg_index = -1

        # Debug: Log message roles found
        message_roles = [msg.get('role', 'unknown') for msg in session_messages]
        logger.info(f"Message roles in session: {message_roles}")

        for i in range(len(session_messages) - 1, -1, -1):
            if session_messages[i].get('role') == 'assistant':
                latest_assistant_msg = session_messages[i]
                assistant_msg_index = i
                logger.info(f"Found assistant message at index {i} with id: {latest_assistant_msg.get('id', 'no-id')}")
                break

        if not latest_assistant_msg:
            logger.error(f"No assistant message found in {len(session_messages)} messages for session {chat_id_obj}")
            raise HTTPException(status_code=404, detail="No assistant response found to attach feedback.")

        feedback_entry = {
            "feedback_type": feedback.feedback_type,
            "feedback_value": feedback.feedback_value,
            "timestamp": feedback.timestamp or datetime.now()
        }
        # Add feedback to the assistant message
        latest_assistant_msg["feedback"] = feedback_entry

        if redis_client is None:
            raise HTTPException(status_code=503, detail="Redis not available for feedback storage.")

        # Update the message in Redis
        session_key = f"session:{user_id_obj}:{chat_id_obj}"
        all_msgs = redis_client.lrange(session_key, 0, -1)
        if all_msgs:
            all_msgs = [json.loads(m) for m in all_msgs]

            # Find and update the assistant message
            for i, msg in enumerate(all_msgs):
                if (msg.get('role') == 'assistant' and
                    msg.get('id') == latest_assistant_msg.get('id')):
                    all_msgs[i] = latest_assistant_msg
                    break

            # Replace all messages in Redis
            redis_client.delete(session_key)
            for m in all_msgs:
                redis_client.rpush(session_key, json.dumps(m))
            redis_client.expire(session_key, SESSION_TTL)

            logger.info(f"Feedback attached to assistant message in session {session_key}")

        # Also store feedback in a separate feedback key for analytics
        feedback_key = f"feedback:{user_id_obj}:{chat_id_obj}"
        feedback_with_context = {
            **feedback_entry,
            "message_id": latest_assistant_msg.get('id'),
            "query": None,  # Find the corresponding user query
            "response": latest_assistant_msg.get('content', '')[:100] + "..." if len(latest_assistant_msg.get('content', '')) > 100 else latest_assistant_msg.get('content', '')
        }

        # Find the user query that corresponds to this assistant response
        for i in range(assistant_msg_index - 1, -1, -1):
            if session_messages[i].get('role') == 'user':
                feedback_with_context["query"] = session_messages[i].get('content', '')
                break

        redis_client.rpush(feedback_key, json.dumps(feedback_with_context))
        redis_client.expire(feedback_key, SESSION_TTL)

        return FeedbackResponse(status="success", feedback=feedback_entry)
    except Exception as e:
        logger.error(f"Error storing feedback: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error storing feedback: {str(e)}")

def get_feedback_for_session(user_id: str, chat_id: str, limit: int = 20) -> list:
    """
    Retrieve feedback entries for a session from Redis.
    """
    user_id_obj = memory_manager._validate_object_id(user_id, "user_id")
    chat_id_obj = memory_manager._validate_object_id(chat_id, "chat_id")

    feedback_key = f"feedback:{user_id_obj}:{chat_id_obj}"
    if redis_client is None:
        return []
    try:
        feedbacks = redis_client.lrange(feedback_key, -limit, -1)
        return [json.loads(fb) for fb in feedbacks]
    except Exception as e:
        logger.error(f"Error retrieving feedback: {str(e)}")
        return []

def get_feedback_context_for_improvement(user_id: str, chat_id: str) -> str:
    """
    Get feedback context to improve future responses.
    Returns a formatted string with feedback insights.
    """
    try:
        user_id_obj = memory_manager._validate_object_id(user_id, "user_id")
        chat_id_obj = memory_manager._validate_object_id(chat_id, "chat_id")

        feedbacks = get_feedback_for_session(user_id_obj, chat_id_obj, limit=10)
        if not feedbacks:
            return ""

        feedback_context = "\n=== PREVIOUS FEEDBACK FOR IMPROVEMENT ===\n"

        positive_feedback = []
        negative_feedback = []
        suggestions = []

        for fb in feedbacks:
            feedback_type = fb.get('feedback_type', '')
            feedback_value = fb.get('feedback_value', '')
            query = fb.get('query', 'Unknown query')
            response_preview = fb.get('response', 'Unknown response')

            if feedback_type == 'thumbs_up' or feedback_value == 'positive':
                positive_feedback.append(f"✅ Query: '{query}' - Response was helpful")
            elif feedback_type == 'thumbs_down' or feedback_value == 'negative':
                negative_feedback.append(f"❌ Query: '{query}' - Response needs improvement")
            elif feedback_type == 'suggestion' and feedback_value:
                suggestions.append(f"💡 Suggestion for '{query}': {feedback_value}")
            elif feedback_type == 'rating' and feedback_value:
                try:
                    rating = float(feedback_value)
                    if rating >= 4:
                        positive_feedback.append(f"⭐ Query: '{query}' - Rated {rating}/5")
                    elif rating <= 2:
                        negative_feedback.append(f"⭐ Query: '{query}' - Rated {rating}/5 (needs improvement)")
                except:
                    pass

        if positive_feedback:
            feedback_context += "POSITIVE FEEDBACK (continue these approaches):\n"
            for fb in positive_feedback[-3:]:  # Last 3 positive feedbacks
                feedback_context += f"  {fb}\n"

        if negative_feedback:
            feedback_context += "AREAS FOR IMPROVEMENT:\n"
            for fb in negative_feedback[-3:]:  # Last 3 negative feedbacks
                feedback_context += f"  {fb}\n"

        if suggestions:
            feedback_context += "USER SUGGESTIONS:\n"
            for suggestion in suggestions[-3:]:  # Last 3 suggestions
                feedback_context += f"  {suggestion}\n"

        feedback_context += "=== END FEEDBACK CONTEXT ===\n"

        return feedback_context

    except Exception as e:
        logger.error(f"Error getting feedback context: {e}")
        return ""

@app.post("/end_session")
async def end_session(request: Request, chat_id: str):
    try:
        #user_id_obj = memory_manager._validate_object_id(user_id, "user_id")
        chat_id_obj = memory_manager._validate_object_id(chat_id, "chat_id")

        user_id_obj = get_user_id(request)
        clear_session(user_id_obj, chat_id_obj)
        client_ip = request.client.host
        if client_ip in active_sessions:
            if active_sessions[client_ip].get("current_chat_id") == chat_id:
                active_sessions[client_ip]["current_chat_id"] = None
            if active_sessions[client_ip].get("last_accessed_history") == chat_id:
                active_sessions[client_ip]["last_accessed_history"] = None
        logger.info(f"Session ended: chat_id={chat_id}, user_id={user_id_obj}")
        return {"message": f"Session {chat_id} cleared successfully"}
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error clearing session for chat_id={chat_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error clearing session: {str(e)}")

@app.get("/multi_agent_status")
async def multi_agent_status():
    """Get Multi-Agent RAG system status"""
    try:
        # Test aggregator agent
        aggregator_status = "initialized" if aggregator_agent else "not_initialized"

        # Test memory manager
        memory_stats = memory_manager.get_memory_stats() if memory_manager else {}

        # Test planning manager
        planning_history = planning_manager.get_planning_history(limit=5) if planning_manager else []

        # Test generative model
        generative_status = "initialized" if generative_model else "not_initialized"

        # Test sub-agents connectivity
        sub_agent_status = {}

        # Test Qdrant agent
        try:
            from server.qdrant_agent import query_qdrant_tool
            test_result = await query_qdrant_tool("test", "Gen AI")
            sub_agent_status["qdrant"] = "connected" if test_result else "disconnected"
        except Exception as e:
            sub_agent_status["qdrant"] = f"error: {str(e)}"

        # Test Serper agent
        try:
            from server.serper_agent import query_google_search
            test_result = await query_google_search("test", 1)
            sub_agent_status["serper"] = "connected" if test_result else "disconnected"
        except Exception as e:
            sub_agent_status["serper"] = f"error: {str(e)}"

        return {
            "multi_agent_rag": {
                "status": "operational",
                "aggregator_agent": aggregator_status,
                "memory_manager": memory_stats,
                "planning_manager": {
                    "status": "initialized" if planning_manager else "not_initialized",
                    "recent_plans": len(planning_history),
                    "last_plan": planning_history[-1]["createdAt"] if planning_history else None
                },
                "generative_model": generative_status,
                "sub_agents": sub_agent_status
            },
            "timestamp": datetime.now()
        }

    except Exception as e:
        return {
            "multi_agent_rag": {
                "status": "error",
                "error": str(e)
            },
            "timestamp": datetime.now()
        }


@app.get("/redis_status")
async def redis_status():
    if redis_client is None:
        return {
            "status": "not_initialized",
            "message": "Redis client was not initialized - running in MongoDB-only mode",
            "host": REDIS_HOST,
            "port": REDIS_PORT,
            "ssl": REDIS_SSL
        }
    try:
        ping_result = redis_client.ping()
        info = redis_client.info()
        memory_info = {
            "used_memory": info.get("used_memory_human", "N/A"),
            "used_memory_peak": info.get("used_memory_peak_human", "N/A"),
            "connected_clients": info.get("connected_clients", "N/A"),
            "total_commands_processed": info.get("total_commands_processed", "N/A"),
            "keyspace_hits": info.get("keyspace_hits", "N/A"),
            "keyspace_misses": info.get("keyspace_misses", "N/A")
        }
        session_keys = redis_client.keys("session:*")
        metadata_keys = redis_client.keys("session_meta:*")
        return {
            "status": "connected",
            "ping": ping_result,
            "host": REDIS_HOST,
            "port": REDIS_PORT,
            "ssl": REDIS_SSL,
            "active_sessions": len(session_keys),
            "active_metadata": len(metadata_keys),
            "memory_info": memory_info,
            "redis_version": info.get("redis_version", "N/A"),
            "uptime_in_seconds": info.get("uptime_in_seconds", "N/A")
        }
    except redis.ConnectionError as e:
        logger.error(f"AWS Redis connection error in status check: {str(e)}")
        return {
            "status": "disconnected",
            "error": str(e),
            "host": REDIS_HOST,
            "port": REDIS_PORT,
            "ssl": REDIS_SSL,
            "message": "Application is running in MongoDB-only mode"
        }
    except Exception as e:
        logger.error(f"Error checking AWS Redis status: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "host": REDIS_HOST,
            "port": REDIS_PORT,
            "ssl": REDIS_SSL,
            "message": "Application is running in MongoDB-only mode"
        }
